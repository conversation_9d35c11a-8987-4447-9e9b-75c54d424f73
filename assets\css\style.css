@charset "UTF-8";
/* Table Of Content - start
================================================== */
/* 
* Project Name   :  Techco – IT Solutions & Technology, Business Consulting, Software Company Site Template
* File           :  CSS Base
* Version        :  1.0.0
* Author         :  XpressBuddy (https://themeforest.net/user/xpressbuddy/portfolio)
* Developer			 :	webrok (https://www.fiverr.com/webrok?up_rollout=true)

==================================================

1 - Template Global Settings
* - 1.1 - Template Fonts
* - 1.2 - Template Reset

2 - Template Elements
* - 2.01 - Template CSS Animations
* - 2.02 - Backtotop Button
* - 2.03 - Template Gapping or Spacing
* - 2.04 - Order & Unorder List
* - 2.05 - Buttons
* - 2.06 - Typography
* - 2.07 - Authorbox
* - 2.08 - Carousel or Slider
* - 2.90 - Form
* - 2.10 - Video
* - 2.11 - Social Icons
* - 2.12 - Counter or Funfact
* - 2.13 - Rating Star
* - 2.14 - Accordion
* - 2.15 - Pagination Nav
* - 2.16 - Icon Block
* - 2.17 - Iconbox
* - 2.18 - Tab

3 - Template Parts
* - 3.01 - Site Header
* - 3.02 - Site Footer
* - 3.03 - Page Header, Page Banner, Breadcrumb
* - 3.04 - Sidebar

4 - Template Components
* - 4.01 - Blog
* - 4.02 - Call To Action
* - 4.03 - Case
* - 4.04 - Client Logo
* - 4.05 - Hero Sections
* - 4.06 - Policy
* - 4.07 - Portfolio
* - 4.08 - Review
* - 4.09 - Service
* - 4.10 - Team

5 - Template Pages
* - 5.01 - About Page
* - 5.02 - Contact Page
* - 5.03 - Details Pages
* - 5.04 - Pricing Page
* - 5.05 - Home Pages

*/
/* Table Of Content - end
================================================== */
:root, [data-bs-theme=light] {
  --bs-body-font-family: 'Axiforma Regular', sans-serif;
  --bs-heading-font-family: 'Axiforma Bold', sans-serif;
  --bs-body-font-size: 16px;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #49515B;
  --bs-body-color-rgb: 73, 81, 91;
  --bs-primary: #0044EB;
  --bs-secondary: #F44380;
  --bs-dark: #020842;
  --bs-light: #E3F0FF;
  --bs-info: #23BABF;
  --bs-warning: #F3A338;
  --bs-danger: #F26F4D;
  --bs-success: #47B16A;
  --bs-primary-rgb: 0, 68, 235;
  --bs-secondary-rgb: 244, 67, 128;
  --bs-dark-rgb: 2, 8, 66;
  --bs-light-rgb: 227, 240, 255;
  --bs-info-rgb: 35, 186, 191;
  --bs-warning-rgb: 243, 163, 56;
  --bs-danger-rgb: 242, 111, 77;
  --bs-success-rgb: 71, 177, 106;
  --bs-primary-bg-subtle: rgba(0, 68, 235, 0.10);
  --bs-secondary-bg-subtle: rgba(244, 67, 128, 0.10);
  --bs-info-bg-subtle: rgba(35, 186, 191, 0.10);
  --bs-warning-bg-subtle: rgba(243, 163, 56, 0.10);
  --bs-danger-bg-subtle: rgba(242, 111, 77, 0.10);
  --bs-success-bg-subtle: rgba(71, 177, 106, 0.10);
  --bs-border-color: #E3F0FF;
  --bs-border-color-translucent: rgba(227, 240, 255, 1);
  --bs-border-radius: 10px;
  --bs-border-radius-sm: 20px;
  --bs-border-radius-pill: 50px;
  --bs-transition: 300ms ease; }

/* 1.1 - Template Fonts - Start
================================================== */
@font-face {
  font-family: 'Axiforma Regular';
  src: url("../fonts/Axiforma-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap; }
@font-face {
  font-family: 'Axiforma Medium';
  src: url("../fonts/Axiforma-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap; }
@font-face {
  font-family: 'Axiforma SemiBold';
  src: url("../fonts/Axiforma-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
  font-display: swap; }
@font-face {
  font-family: 'Axiforma Bold';
  src: url("../fonts/Axiforma-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap; }
/* 1.1 - Template Fonts - End
================================================== */
/* 1.2 - Template Reset - Start
================================================== */
body {
  margin: 0;
  padding: 0;
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  font-style: normal;
  color: var(--bs-body-color);
  font-family: var(--bs-body-font-family);
  background-color: var(--bs-body-bg);
  text-rendering: optimizelegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased; }

.page_wrapper {
  position: relative; }

iframe {
  border: none; }

a:focus,
a:active,
input,
input:hover,
input:focus,
input:active,
textarea,
textarea:hover,
textarea:focus,
textarea:active {
  outline: none; }

img:not([draggable]),
embed,
object,
video {
  height: auto;
  max-width: 100%; }

img {
  border: none;
  height: auto;
  max-width: 100%;
  user-select: none;
  vertical-align: middle; }

a {
  outline: 0;
  display: inline-block;
  text-decoration: none;
  transition: var(--bs-transition);
  font-family: var(--bs-body-font-family); }
  a:active, a:focus, a:hover, a:visited {
    outline: 0;
    text-decoration: none; }

button {
  padding: 0px;
  border: none;
  outline: none;
  background: none;
  display: inline-block;
  transition: var(--bs-transition);
  font-family: var(--bs-body-font-family); }
  button:focus {
    outline: none; }

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  color: var(--bs-dark);
  font-family: var(--bs-heading-font-family); }
  h1:has(b), h2:has(b), h3:has(b), h4:has(b), h5:has(b), h6:has(b) {
    font-weight: 300; }
    h1:has(b) b, h2:has(b) b, h3:has(b) b, h4:has(b) b, h5:has(b) b, h6:has(b) b {
      font-weight: 700; }
  h1:has(strong), h2:has(strong), h3:has(strong), h4:has(strong), h5:has(strong), h6:has(strong) {
    font-weight: 300; }
    h1:has(strong) strong, h2:has(strong) strong, h3:has(strong) strong, h4:has(strong) strong, h5:has(strong) strong, h6:has(strong) strong {
      font-weight: 700; }

b, strong {
  font-weight: 700;
  font-family: 'Axiforma Bold'; }

hr {
  opacity: 1;
  height: 1px;
  border: none;
  margin: 25px 0;
  background-color: var(--bs-border-color); }

mark {
  padding: 0;
  color: var(--bs-primary);
  background-color: transparent; }

.container {
  max-width: 1320px;
  padding-left: 15px;
  padding-right: 15px; }

.container-fluid {
  padding-left: 30px;
  padding-right: 30px; }

.row {
  margin: -15px; }

[class*="col-"] {
  padding: 15px; }

.xb-hidden {
  overflow: hidden; }

.pt-130 {
  padding-top: 130px; }

.pt-180 {
  padding-top: 180px; }

.pt-170 {
  padding-top: 170px; }

.pb-130 {
  padding-bottom: 130px; }

.pb-80 {
  padding-bottom: 80px; }

.dropdown-menu {
  padding: 6px 0;
  margin-top: 20px;
  transition: 200ms;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius);
  border: 1px solid var(--bs-border-color);
  box-shadow: 0 20px 40px 0 rgba(2, 8, 66, 0.1);
  z-index: -1; }
  .dropdown-menu:before {
    left: 0;
    right: 0;
    top: -20px;
    content: '';
    height: 20px;
    display: block;
    position: absolute; }
  .dropdown-menu > li {
    padding: 0 6px; }
    .dropdown-menu > li:not(:last-child) {
      margin-bottom: 1px; }
    .dropdown-menu > li > a {
      gap: 8px;
      display: flex;
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
      position: relative;
      border-radius: 7px;
      align-items: center;
      white-space: nowrap;
      color: var(--bs-dark);
      padding: 12px 22px 8px;
      justify-content: space-between; }
    .dropdown-menu > li:hover > a, .dropdown-menu > li.active > a {
      color: var(--bs-primary);
      background-color: var(--bs-primary-bg-subtle); }

.dropdown-toggle::after {
  margin: 0;
  border: none;
  line-height: 1;
  content: '\f107';
  font-weight: 400;
  color: var(--bs-body-color);
  font-family: 'Font Awesome 6 Pro'; }

@media screen and (min-width: 992px) {
  .dropdown-menu {
    left: 0;
    transform: translateY(4px); }
    .dropdown-menu > .dropdown > .dropdown-menu {
      left: 100%;
      transform: translateY(4px); }

  .dropdown:hover > .dropdown-menu {
    display: block;
    transform: translateY(0px); }
    .dropdown:hover > .dropdown-menu > .dropdown > .dropdown-menu {
      transform: translateY(0px); } }
.badge {
  line-height: 1;
  font-size: 10px;
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 3px;
  letter-spacing: 0.5px;
  font-family: 'Axiforma Medium'; }

.overlay {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  position: absolute; }

.section_decoration {
  z-index: 1;
  position: relative; }
  .section_decoration .decoration_item {
    z-index: -1;
    position: absolute; }

/* 1.2 - Template Reset - End
================================================== */
/* 2.01 - Template CSS Animations - Start
================================================== */
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-20px); }
  100% {
    opacity: 1;
    transform: translateY(0); } }
.fadeInDown {
  animation-name: fadeInDown; }

@keyframes phoneRinging {
  from {
    transform: rotate3d(0, 0, 1, 0deg); }
  20%, 32%, 44%, 56%, 68% {
    transform: rotate3d(0, 0, 1, 0deg); }
  23%, 35%, 47%, 59%, 71% {
    transform: rotate3d(0, 0, 1, 15deg); }
  26%, 38%, 50%, 62%, 74% {
    transform: rotate3d(0, 0, 1, 0deg); }
  29%, 41%, 53%, 65%, 77% {
    transform: rotate3d(0, 0, 1, -15deg); }
  80% {
    transform: rotate3d(0, 0, 1, 0deg); } }
[class*="fa-phone"] {
  animation: phoneRinging 1.5s infinite linear; }

@keyframes hueRotate {
  from {
    filter: hue-rotate(0); }
  to {
    filter: hue-rotate(360deg); } }
.hueRotate {
  animation: hueRotate 3s linear infinite;
  background: linear-gradient(0deg, #FAEC60, #F3A338, #F37528, #F44380, #BE3DB3, #0044EB, #5A71F1, #439EFF); }

@keyframes ripple {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: scale(1.75); } }
.ripple {
  animation: ripple 1.5s linear infinite; }

@keyframes spin {
  from {
    transform: rotate(0deg); }
  to {
    transform: rotate(360deg); } }
.spin {
  animation: spin 5000ms infinite linear; }

@keyframes spinReverse {
  from {
    transform: rotate(360deg); }
  to {
    transform: rotate(0deg); } }
.spinReverse {
  animation: spinReverse 5000ms infinite linear; }

@keyframes borderDashedAnimation {
  0% {
    background-position: left top, right bottom, left bottom, right top; }
  100% {
    background-position: left 15px top, right 15px bottom, left bottom 15px, right top 15px; } }
.borderDashedAnimation {
  animation: borderDashedAnimation .5s infinite linear; }

@keyframes preloaderSpin {
  0% {
    transform: rotate(0); }
  100% {
    transform: rotate(360deg); } }
.preloader {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  z-index: 999999;
  background-color: var(--bs-dark); }

.preloader .loader-circle {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 200px;
  height: 200px;
  direction: ltr;
  position: absolute;
  border-radius: 100%;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1); }

.preloader .loader-line-mask {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 30px;
  height: 200px;
  margin-left: -100px;
  margin-top: -100px;
  overflow: hidden;
  transform-origin: 100px 100px;
  animation: preloaderSpin 1.5s infinite linear; }

.preloader .loader-line {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.44), rgba(255, 255, 255, 0));
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='rgba(255, 255, 255, 0.44)',endColorStr='rgba(255, 255, 255, 0)');
  background-color: initial;
  position: relative; }

.preloader .loader-line:before {
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  z-index: 99;
  content: '';
  position: absolute;
  border-radius: inherit;
  background-color: var(--bs-dark); }

.preloader .loader-logo {
  top: 50%;
  left: 0;
  right: 0;
  position: absolute;
  text-align: center;
  transform: translate(0%, -50%); }

.preloader .loader-logo img {
  max-width: 65%; }

/* 2.01 - Template CSS Animations - End
================================================== */
/* 2.02 - Backtotop Button - Start
================================================== */
.backtotop {
  right: 15px;
  z-index: 999;
  bottom: 58px;
  display: none;
  position: fixed; }
  .backtotop .scroll {
    z-index: 1;
    width: 42px;
    height: 42px;
    display: flex;
    font-size: 15px;
    position: relative;
    align-items: center;
    border-radius: 100%;
    color: var(--bs-dark);
    justify-content: center;
    background-color: var(--bs-white);
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }
    .backtotop .scroll:hover {
      color: var(--bs-dark); }
    .backtotop .scroll i {
      animation: bttIconMover 1s infinite alternate; }

body:has(.footer_layout_1) .backtotop {
  bottom: 72px; }

/* 2.02 - Backtotop Button - End
================================================== */
/* 2.03 - Template Gapping or Spacing - Start
================================================== */
.section_space {
  padding-top: 120px;
  padding-bottom: 120px; }

/* 2.03 - Template Gapping or Spacing - End
================================================== */
/* 2.05 - Buttons - Start
================================================== */
.btns_group {
  gap: 30px;
  display: flex;
  padding: 50px 0;
  align-items: center;
  justify-content: center; }

.btn {
  gap: 10px;
  line-height: 1;
  padding: 0 40px;
  font-size: 14px;
  overflow: hidden;
  font-weight: 600;
  transition: 300ms;
  position: relative;
  align-items: center;
  letter-spacing: 1px;
  display: inline-flex;
  color: var(--bs-white);
  justify-content: center;
  text-transform: uppercase;
  font-family: 'Axiforma SemiBold';
  background-color: var(--bs-primary);
  border: 1px solid var(--bs-primary);
  border-radius: var(--bs-border-radius-pill); }
  .btn .btn_icon {
    line-height: 1;
    font-size: 16px; }
  .btn .btn_label {
    position: relative;
    display: inline-block;
    padding: 23px 0px 21px;
    transition: transform 0.4s cubic-bezier(0.15, 0.85, 0.31, 1); }
    .btn .btn_label:before {
      top: 120%;
      left: 50%;
      width: 100%;
      display: block;
      position: absolute;
      content: attr(data-text);
      transform: translateX(-50%); }
  .btn:hover {
    color: var(--bs-white);
    transform: translateY(-2px);
    background-color: var(--bs-primary);
    border: 1px solid var(--bs-primary);
    box-shadow: 0 10px 30px 0 rgba(0, 68, 235, 0.26); }
    .btn:hover .btn_label {
      transform: translateY(-81%); }

.btn-dark {
  border-color: var(--bs-dark);
  background-color: var(--bs-dark); }

.btn-light {
  color: var(--bs-dark);
  border-color: var(--bs-light);
  background-color: var(--bs-light); }

.btn-warning {
  --bs-warning: #FFBE16;
  color: var(--bs-dark);
  border-color: var(--bs-warning);
  background-color: var(--bs-warning); }
  .btn-warning:hover {
    color: var(--bs-dark);
    border-color: var(--bs-warning);
    background-color: var(--bs-warning); }

[class*="-outline-light"] {
  color: var(--bs-dark);
  background-color: transparent;
  border-color: var(--bs-border-color); }
  [class*="-outline-light"]:hover {
    color: var(--bs-white);
    border-color: var(--bs-primary);
    background-color: var(--bs-primary);
    box-shadow: 0 10px 30px 0 rgba(0, 68, 235, 0.26); }

.btn_unfill {
  gap: 10px;
  font-size: 18px;
  line-height: 32px;
  align-items: center;
  display: inline-flex;
  color: var(--bs-body-color); }
  .btn_unfill .btn_icon {
    width: 40px;
    height: 40px;
    flex: 0 0 auto;
    border-radius: 100%;
    align-items: center;
    display: inline-flex;
    color: var(--bs-white);
    justify-content: center;
    transition: var(--bs-transition);
    background-color: var(--bs-dark); }
  .btn_unfill:hover {
    color: var(--bs-dark); }
    .btn_unfill:hover .btn_icon {
      color: var(--bs-white);
      background-color: var(--bs-primary); }

.creative_btn {
  font-size: 18px;
  font-weight: 600;
  align-items: center;
  display: inline-flex;
  color: var(--bs-white);
  justify-content: center;
  font-family: 'Axiforma SemiBold'; }
  .creative_btn .btn_label {
    border-radius: 50px;
    padding: 17px 50px 16px; }
  .creative_btn .btn_icon {
    width: 60px;
    height: 60px;
    flex: 0 0 auto;
    overflow: hidden;
    position: relative;
    border-radius: 100%; }
    .creative_btn .btn_icon i {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      transition: .3s;
      position: absolute;
      border-radius: 100%;
      align-items: center;
      justify-content: center; }
      .creative_btn .btn_icon i:nth-child(2) {
        left: -150%;
        bottom: -150%;
        transform: scale(0); }
  .creative_btn:hover .btn_icon i:nth-child(1) {
    top: -150%;
    right: -150%;
    transform: scale(0); }
  .creative_btn:hover .btn_icon i:nth-child(2) {
    left: 0%;
    bottom: 0%;
    transform: scale(1); }

/* 2.05 - Buttons - End
================================================== */
/* 2.06 - Typography - Start
================================================== */
.heading_block {
  margin-bottom: 40px; }
  .heading_block .heading_text {
    font-size: 45px;
    line-height: 60px;
    margin-bottom: 16px;
    letter-spacing: -1px; }
  .heading_block .heading_description {
    font-size: 18px;
    font-weight: 500;
    line-height: 30px;
    font-family: 'Axiforma Medium'; }

.heading_focus_text:has(.badge) {
  gap: 8px;
  align-items: center;
  margin-bottom: 18px;
  display: inline-flex; }

.heading_focus_text .badge {
  font-size: 14px;
  font-weight: 500;
  margin-top: -3px;
  padding: 4px 7px 3px; }

.heading_focus_text.has_underline {
  z-index: 1;
  font-weight: 500;
  position: relative;
  margin-bottom: 26px;
  color: var(--bs-dark);
  padding: 0 36px 6px 0;
  text-transform: uppercase;
  background-size: auto 18px;
  background-repeat: no-repeat;
  font-family: 'Axiforma Medium';
  background-position: right bottom; }

.text-white .heading_focus_text.has_underline,
.text-white .heading_text {
  color: var(--bs-white); }
.text-white .heading_description {
  opacity: 0.8;
  color: var(--bs-white); }

.text-center .heading_description {
  margin: auto;
  max-width: 610px; }

/* 2.06 - Typography - End
================================================== */
/* 2.04 - Order & Unorder List - Start
================================================== */
[class*="unordered_list"] {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center; }
  [class*="unordered_list"] > li {
    float: left;
    list-style: none;
    display: inline-block; }

.unordered_list_block {
  display: flex;
  flex-direction: column; }
  .unordered_list_block > li {
    width: 100%;
    display: block; }

.icon_list.unordered_list_block {
  gap: 8px; }
.icon_list > li {
  gap: 10px;
  display: flex;
  font-size: 16px;
  line-height: 28px;
  align-items: flex-start; }
.icon_list a {
  gap: 10px;
  display: inline-flex;
  align-items: flex-start;
  color: var(--bs-body-color); }
  .icon_list a:hover {
    color: var(--bs-primary); }
.icon_list .icon_list_icon:has(.fa-circle) {
  font-size: 6px;
  margin: -1px 0 0;
  color: var(--bs-primary); }
.icon_list .icon_list_icon img {
  width: 24px;
  margin-top: -2px; }

.category_btns_group {
  gap: 10px; }
  .category_btns_group a {
    display: flex;
    line-height: 1;
    font-size: 14px;
    font-weight: 500;
    border-radius: 5px;
    align-items: center;
    padding: 8px 10px 6px;
    justify-content: center;
    color: var(--bs-body-color);
    font-family: 'Axiforma Medium';
    border: 1px solid var(--bs-border-color); }
    .category_btns_group a:hover {
      color: var(--bs-white);
      border-color: var(--bs-primary);
      background-color: var(--bs-primary); }

.category_list {
  gap: 20px; }
  .category_list > li {
    line-height: 1;
    font-size: 16px; }
  .category_list a {
    color: var(--bs-body-color); }
    .category_list a:hover {
      color: var(--bs-primary); }

.post_category_list > li {
  border-bottom: 1px solid #CCE3FF; }
.post_category_list a {
  display: flex;
  line-height: 1;
  font-size: 18px;
  position: relative;
  align-items: center;
  padding: 18px 0 17px;
  color: var(--bs-body-color);
  justify-content: space-between; }
  .post_category_list a i {
    top: 17px;
    left: -10px;
    opacity: 0;
    font-size: 20px;
    position: absolute;
    color: var(--bs-primary);
    transition: var(--bs-transition); }
  .post_category_list a span {
    transition: var(--bs-transition); }
  .post_category_list a:hover i {
    left: 0;
    opacity: 1; }
  .post_category_list a:hover span {
    color: var(--bs-primary); }
    .post_category_list a:hover span:nth-child(2) {
      transform: translateX(22px); }

.post_meta {
  gap: 20px; }
  .post_meta > li {
    font-size: 16px; }
    .post_meta > li i {
      color: var(--bs-dark); }
    .post_meta > li img, .post_meta > li i {
      margin: -2px 0 0; }
    .post_meta > li a {
      gap: 4px;
      align-items: center;
      display: inline-flex;
      color: var(--bs-body-color); }
      .post_meta > li a:hover {
        color: var(--bs-dark); }

.tags_list {
  gap: 10px; }
  .tags_list a {
    display: block;
    font-size: 14px;
    border-radius: 5px;
    padding: 8px 18px 7px;
    color: var(--bs-body-color);
    background-color: var(--bs-white);
    box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }
    .tags_list a:hover {
      color: var(--bs-white);
      background-color: var(--bs-primary); }

.filter_elements_nav {
  display: flex;
  margin-bottom: 70px;
  justify-content: center; }
  .filter_elements_nav > ul {
    gap: 3px;
    padding: 7px;
    background-color: var(--bs-white);
    border-radius: var(--bs-border-radius); }
    .filter_elements_nav > ul > li {
      line-height: 1;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      color: var(--bs-dark);
      padding: 16px 30px 13px;
      text-transform: uppercase;
      transition: var(--bs-transition);
      font-family: 'Axiforma SemiBold';
      border-radius: var(--bs-border-radius); }
      .filter_elements_nav > ul > li:not(.active):hover {
        background-color: var(--bs-light); }
      .filter_elements_nav > ul > li.active {
        color: var(--bs-white);
        background-color: var(--bs-primary); }

/* 2.04 - Order & Unorder List - End
================================================== */
/* 2.90 - Form - Start
================================================== */
.form-group {
  position: relative; }
  .form-group:not(:last-child) {
    margin-bottom: 30px; }
  .form-group .form-control,
  .form-group .form-select {
    padding: 0 24px;
    font-size: 16px;
    color: var(--bs-dark);
    transition: 0.3s ease;
    caret-color: var(--bs-primary);
    background-color: var(--bs-white);
    border-radius: var(--bs-border-radius);
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }
    .form-group .form-control:focus,
    .form-group .form-select:focus {
      border-color: var(--bs-primary); }
    .form-group .form-control::placeholder,
    .form-group .form-select::placeholder {
      color: #676767;
      font-size: 16px;
      transform: translateY(3px); }
    .form-group .form-control:-ms-input-placeholder,
    .form-group .form-select:-ms-input-placeholder {
      color: #676767;
      font-size: 16px;
      transform: translateY(3px); }
    .form-group .form-control::-ms-input-placeholder,
    .form-group .form-select::-ms-input-placeholder {
      color: #676767;
      font-size: 16px;
      transform: translateY(3px); }
  .form-group input.form-control,
  .form-group select.form-select {
    height: 60px; }
  .form-group textarea.form-control {
    min-height: 180px;
    border-radius: 12px;
    padding: 15px 25px 25px; }
  .form-group .form-select {
    color: #8C8F92; }

.input_title {
  display: block;
  line-height: 1;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 17px;
  color: var(--bs-dark);
  font-family: 'Axiforma SemiBold'; }

input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
  -webkit-appearance: none; }

.search_form button[type=submit] {
  top: 19px;
  right: 20px;
  opacity: 0.5;
  position: absolute; }
  .search_form button[type=submit]:hover {
    opacity: 1; }
  .search_form button[type=submit] img {
    width: 16px; }

.form-check {
  padding-left: 30px; }
  .form-check .form-check-input {
    width: 20px;
    height: 20px;
    box-shadow: none;
    margin: 0 0 0 -30px; }
  .form-check .form-check-label {
    cursor: pointer; }

/* 2.90 - Form - End
================================================== */
/* 2.08 - Carousel or Slider - Start
================================================== */
.swiper-pagination-bullets {
  display: flex;
  position: static;
  padding: 60px 0 0;
  justify-content: center; }
  .swiper-pagination-bullets .swiper-pagination-bullet {
    width: 10px;
    height: 10px; }
    .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active {
      background-color: var(--bs-primary); }

/* 2.08 - Carousel or Slider - End
================================================== */
/* 2.16 - Icon Block - Start
================================================== */
.icon_block {
  width: 60px;
  height: 60px;
  flex: 0 0 auto;
  font-size: 20px;
  overflow: hidden;
  border-radius: 100%;
  align-items: center;
  display: inline-flex;
  color: var(--bs-dark);
  justify-content: center;
  border: 1px solid var(--bs-light);
  background-color: var(--bs-light); }
  .icon_block:hover {
    color: var(--bs-white);
    border-color: var(--bs-primary);
    background-color: var(--bs-primary); }

/* 2.16 - Icon Block - End
================================================== */
/* 2.17 - Iconbox - Start
================================================== */
.iconbox_block {
  padding: 50px 50px 42px;
  transition: var(--bs-transition);
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius-sm);
  box-shadow: 0 1px 2px 0 rgba(174, 191, 210, 0.3); }
  .iconbox_block:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }
  .iconbox_block .iconbox_icon {
    width: 80px;
    height: 80px;
    flex: 0 0 auto;
    margin: 0 0 32px;
    border-radius: 100%;
    align-items: center;
    display: inline-flex;
    justify-content: center;
    color: var(--bs-primary);
    background-color: var(--bs-primary-bg-subtle); }
    .iconbox_block .iconbox_icon img {
      max-width: 40px; }
  .iconbox_block .iconbox_title {
    font-size: 30px;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 20px;
    font-family: 'Axiforma SemiBold'; }
  .iconbox_block p {
    font-size: 16px; }

.iconbox_block.layout_icon_left {
  padding: 30px;
  display: inline-flex;
  align-items: flex-start;
  border-radius: var(--bs-border-radius); }
  .iconbox_block.layout_icon_left .iconbox_icon {
    width: 70px;
    height: 70px;
    margin: 0 30px 0 0;
    border-radius: 10px; }
    .iconbox_block.layout_icon_left .iconbox_icon img {
      max-width: 32px; }
  .iconbox_block.layout_icon_left .iconbox_title {
    font-size: 20px;
    line-height: 28px;
    margin-bottom: 12px; }

.iconbox_block_2 {
  display: block;
  color: var(--bs-dark);
  padding: 15px 25px 10px 15px;
  transition: var(--bs-transition);
  border-radius: var(--bs-border-radius); }
  .iconbox_block_2 .icon_title_wrap {
    gap: 10px;
    display: flex;
    margin-bottom: 10px;
    align-items: flex-start; }
  .iconbox_block_2 .iconbox_icon {
    width: 30px;
    height: 30px;
    flex: 0 0 auto;
    border-radius: 5px;
    align-items: center;
    display: inline-flex;
    justify-content: center;
    background-color: rgba(0, 68, 235, 0.1); }
    .iconbox_block_2 .iconbox_icon img {
      max-width: 16px; }
  .iconbox_block_2 .iconbox_title {
    line-height: 1;
    margin: 6px 0 0;
    font-size: 20px;
    font-weight: 600;
    font-family: 'Axiforma SemiBold'; }
  .iconbox_block_2 .badge {
    margin-top: 5px; }
  .iconbox_block_2 .description {
    font-size: 15px;
    color: var(--bs-body-color); }
  .iconbox_block_2:hover {
    background-color: var(--bs-light); }

/* 2.17 - Iconbox - End
================================================== */
/* 2.18 - Tab - Start
================================================== */
.tab_block_wrapper {
  display: flex;
  flex-direction: column; }
  .tab_block_wrapper .nav {
    gap: 3px;
    padding: 10px;
    align-self: center;
    margin-bottom: 60px;
    background-color: #CCE3FF;
    border-radius: var(--bs-border-radius); }
    .tab_block_wrapper .nav button {
      display: flex;
      line-height: 1;
      font-size: 18px;
      font-weight: 500;
      border-radius: 5px;
      align-items: center;
      color: var(--bs-dark);
      padding: 13px 40px 9px;
      justify-content: center;
      font-family: 'Axiforma Medium'; }
      .tab_block_wrapper .nav button.active {
        background-color: var(--bs-white);
        box-shadow: 0 4px 4px 0 rgba(174, 191, 210, 0.3); }

/* 2.18 - Tab - End
================================================== */
/* 2.12 - Counter or Funfact - Start
================================================== */
.funfact_block {
  position: relative;
  padding: 40px 40px 30px;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius-sm);
  box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3); }
  .funfact_block .funfact_icon {
    height: 80px;
    margin-bottom: 59px; }
    .funfact_block .funfact_icon img {
      max-height: 80px; }
  .funfact_block .counter_value {
    line-height: 1;
    font-size: 45px;
    font-weight: 700;
    align-items: center;
    margin-bottom: 10px;
    display: inline-flex;
    color: var(--bs-dark);
    font-family: 'Axiforma Bold'; }
  .funfact_block .funfact_title {
    font-size: 16px;
    font-weight: 400;
    line-height: 28px;
    color: var(--bs-body-color);
    font-family: 'Axiforma Regular'; }
  .funfact_block .bottom_line {
    bottom: 0;
    left: 50%;
    width: 55%;
    height: 4px;
    position: absolute;
    transform: translateX(-50%); }

.funfact_block:has(.bottom_line) {
  box-shadow: none;
  padding: 75px 40px 65px;
  border: 1px solid #E7E9EE; }
  .funfact_block:has(.bottom_line) .counter_value {
    font-size: 65px;
    margin-bottom: 20px; }
  .funfact_block:has(.bottom_line) .funfact_title {
    font-size: 18px;
    font-weight: 600;
    font-family: 'Axiforma SemiBold'; }

.funfact_block.capsule_layout {
  padding: 6px;
  display: flex;
  align-items: center;
  border-radius: 50px;
  justify-content: space-between;
  box-shadow: 0px 5px 10px 0 rgba(0, 0, 0, 0.15); }
  .funfact_block.capsule_layout .funfact_content {
    gap: 10px;
    padding: 0 40px;
    display: inline-flex;
    flex-direction: column; }
  .funfact_block.capsule_layout .counter_value {
    font-size: 30px;
    font-weight: 600;
    margin-bottom: 0px;
    font-family: "Axiforma SemiBold"; }
  .funfact_block.capsule_layout .funfact_title {
    line-height: 1;
    font-size: 14px; }
  .funfact_block.capsule_layout .funfact_icon {
    margin: 0;
    width: 78px;
    height: 78px;
    flex: 0 0 auto;
    align-items: center;
    border-radius: 100%;
    display: inline-flex;
    justify-content: center;
    background-color: #47B16A; }

.our_world_employees {
  position: relative; }
  .our_world_employees .image_wrap {
    overflow: hidden;
    position: relative;
    border-radius: var(--bs-border-radius-sm); }
    .our_world_employees .image_wrap:before {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      content: '';
      position: absolute;
      background: linear-gradient(0deg, var(--bs-primary), transparent 70%); }
  .our_world_employees .content_wrap {
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    position: absolute;
    padding: 34px 34px 37px; }
  .our_world_employees .title_text {
    font-size: 36px;
    font-weight: 700;
    line-height: 48px;
    letter-spacing: -1px;
    color: var(--bs-white);
    font-family: 'Axiforma Bold'; }

/* 2.12 - Counter or Funfact - End
================================================== */
/* 2.07 - Authorbox - Start
================================================== */
.author_box {
  gap: 26px;
  z-index: 1;
  position: relative;
  align-items: center;
  display: inline-flex; }
  .author_box .author_image {
    width: 90px;
    height: 110px;
    flex: 0 0 auto;
    border-radius: 5px;
    display: inline-flex;
    align-items: flex-end;
    justify-content: center;
    padding: 10px 2px 0 2px; }
  .author_box .author_name {
    line-height: 1;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 14px;
    font-family: 'Axiforma SemiBold'; }
  .author_box .author_designation {
    display: block;
    line-height: 1;
    font-size: 14px;
    color: var(--bs-body-color); }
  .author_box .quote_icon {
    top: 0;
    right: 0;
    z-index: -1;
    max-width: 119px;
    position: absolute; }

/* 2.07 - Authorbox - End
================================================== */
/* 2.10 - Video - Start
================================================== */
.mfp-iframe-holder .mfp-content {
  width: 100%;
  line-height: 0;
  max-width: 70%;
  border: 4px solid #ffffff; }

.mfp-container {
  padding: 0 15px; }

.mfp-bg {
  opacity: 0.94;
  background-color: #000000; }

@media screen and (max-width: 1199px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 100%; } }
.video_btn {
  gap: 10px;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  align-items: center;
  display: inline-flex;
  color: var(--bs-dark);
  font-family: 'Axiforma Medium'; }
  .video_btn .btn_icon {
    width: 58px;
    height: 58px;
    flex: 0 0 58px;
    font-size: 20px;
    padding-left: 4px;
    border-radius: 100%;
    align-items: center;
    display: inline-flex;
    color: var(--bs-dark);
    justify-content: center;
    transition: var(--bs-transition);
    border: 2px solid var(--bs-white);
    outline: 1px solid var(--bs-dark); }
  .video_btn:hover .btn_icon {
    color: var(--bs-white);
    background-color: var(--bs-dark); }

.video_btn.ripple_effect .btn_icon {
  z-index: 1;
  padding: 0;
  width: auto;
  height: auto;
  padding: 20px;
  outline: none;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid var(--bs-white);
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 20px 30px 0 rgba(2, 8, 66, 0.5); }
  .video_btn.ripple_effect .btn_icon:before, .video_btn.ripple_effect .btn_icon:after {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: -1;
    content: '';
    position: absolute;
    border-radius: 100%;
    transition: all .33s ease;
    animation: ripple 1.5s linear infinite;
    border: 1px solid rgba(255, 255, 255, 0.5); }
  .video_btn.ripple_effect .btn_icon:before {
    animation-delay: .1s; }
  .video_btn.ripple_effect .btn_icon:after {
    animation-delay: .9s; }
  .video_btn.ripple_effect .btn_icon i {
    width: 86px;
    height: 86px;
    flex: 0 0 auto;
    transition: 0.2s;
    padding-left: 4px;
    border-radius: 100%;
    align-items: center;
    display: inline-flex;
    color: var(--bs-white);
    justify-content: center;
    background-color: var(--bs-primary); }

.position-relative .video_btn {
  top: 50%;
  left: 50%;
  z-index: 2;
  position: absolute;
  transform: translate(-50%, -50%); }

/* 2.10 - Video - End
================================================== */
/* 2.11 - Social Icons - Start
================================================== */
.social_icons_block {
  gap: 10px; }
  .social_icons_block a {
    width: 40px;
    height: 40px;
    display: flex;
    flex: 0 0 auto;
    border-radius: 5px;
    align-items: center;
    justify-content: center;
    background-color: var(--bs-white);
    border: 1px solid var(--bs-light); }
    .social_icons_block a:hover {
      color: var(--bs-white);
      border-color: var(--bs-primary);
      background-color: var(--bs-primary); }
      .social_icons_block a:hover img {
        filter: invert(1); }

.social_links_block {
  gap: 6px; }
  .social_links_block a {
    display: flex;
    line-height: 1;
    font-size: 12px;
    align-items: center;
    border-radius: 50px;
    color: var(--bs-dark);
    padding: 12px 16px 10px;
    justify-content: center;
    text-transform: uppercase;
    border: 1px solid var(--bs-border-color); }
    .social_links_block a:hover {
      color: var(--bs-white);
      border-color: var(--bs-primary);
      background-color: var(--bs-primary); }

/* 2.11 - Social Icons - End
================================================== */
/* 2.13 - Rating Star - Start
================================================== */
.rating_block {
  gap: 2px; }
  .rating_block li {
    line-height: 1;
    font-size: 14px; }
    .rating_block li i {
      color: #FCC640; }

/* 2.13 - Rating Star - End
================================================== */
/* 2.14 - Accordion - Start
================================================== */
.accordion .accordion-item {
  border-radius: 0;
  border-style: solid;
  border-color: #CCE3FF;
  border-width: 0 0 1px;
  background-color: transparent; }
.accordion .accordion-button {
  font-size: 20px;
  font-weight: 500;
  line-height: 36px;
  padding: 16px 0 11px;
  color: var(--bs-dark);
  border-radius: 0 !important;
  background-color: transparent;
  font-family: 'Axiforma Medium'; }
  .accordion .accordion-button:hover {
    color: var(--bs-primary); }
  .accordion .accordion-button:after {
    width: auto;
    height: auto;
    content: "\2b";
    font-size: 16px;
    background: none;
    font-weight: 700;
    font-family: 'Font Awesome 5 pro'; }
  .accordion .accordion-button[aria-expanded=true]:after {
    content: '\f068'; }
.accordion .accordion-body {
  padding: 20px 34px;
  background-color: #CCE3FF; }
.accordion p {
  font-size: 16px;
  line-height: 30px; }

.faq_accordion .accordion-button {
  font-size: 24px;
  padding: 36px 0 31px; }
  .faq_accordion .accordion-button:after {
    width: 40px;
    height: 40px;
    font-size: 18px;
    transform: unset;
    line-height: 40px;
    text-align: center;
    border-radius: 5px;
    background: var(--bs-white);
    border: 1px solid var(--bs-border-color); }
.faq_accordion .accordion-button[aria-expanded=true]:after {
  color: var(--bs-white);
  border-color: var(--bs-primary);
  background-color: var(--bs-primary); }
.faq_accordion .accordion-body {
  position: relative;
  border-radius: 5px;
  padding: 43px 50px 40px 170px; }
.faq_accordion .text_a {
  top: 50px;
  left: 70px;
  line-height: 1;
  font-size: 45px;
  position: absolute;
  color: var(--bs-dark);
  font-family: 'Axiforma SemiBold'; }
.faq_accordion .accordion-item,
.faq_accordion .accordion-body {
  border-color: #E3F0FF; }
.faq_accordion .accordion-item:has([aria-expanded=true]) {
  border-color: transparent; }
.faq_accordion .accordion-button:not(.collapsed) {
  box-shadow: none; }

.faq_section {
  padding-top: 170px; }
  .faq_section .shape_image_1 {
    top: 0;
    right: 0;
    width: 100%; }
    .faq_section .shape_image_1 img {
      width: calc(100% - 265px); }
  .faq_section .shape_image_2 {
    top: 0;
    right: 0;
    max-width: 265px; }

/* 2.14 - Accordion - End
================================================== */
/* 2.15 - Pagination Nav - Start
================================================== */
.pagination_wrap {
  padding: 40px 0; }

.pagination_nav {
  gap: 15px; }
  .pagination_nav a {
    width: 50px;
    height: 50px;
    display: block;
    font-size: 16px;
    font-weight: 600;
    line-height: 54px;
    border-radius: 5px;
    text-align: center;
    color: var(--bs-dark);
    font-family: "Axiforma Medium";
    background-color: var(--bs-white);
    box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }
  .pagination_nav > li:hover:not(.active) > a {
    color: var(--bs-white);
    background-color: var(--bs-primary); }
  .pagination_nav > li.active > a {
    color: var(--bs-white);
    background-color: var(--bs-dark); }

/* 2.15 - Pagination Nav - End
================================================== */
/* 3.01 - Site Header - Start
================================================== */
.site_header {
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  position: absolute;
  transition: transform .5s; }
  .site_header .site_logo .site_link {
    max-width: 124px; }
  .site_header .site_logo .badge {
    margin-left: 5px; }
  .site_header .header_btns_group {
    gap: 15px;
    display: flex;
    align-items: center; }
    .site_header .header_btns_group > li:first-child {
      display: none; }
  .site_header .header_btns_group .btn {
    padding: 0 30px; }
    .site_header .header_btns_group .btn .btn_label {
      padding: 19px 0px 16px; }
    .site_header .header_btns_group .btn:hover .btn_label {
      transform: translateY(-81%); }

.main_menu_list {
  gap: 48px; }
  .main_menu_list > li > a {
    z-index: 1;
    display: flex;
    line-height: 1;
    color: inherit;
    display: block;
    font-size: 15px;
    position: relative;
    align-items: center;
    color: var(--bs-dark);
    font-family: 'Axiforma Medium';
    min-height: 50px;
    display: flex;
    align-items: center;
    display: flex;
    align-items: center; }
    @media (min-width: 992px) {
      .main_menu_list > li > a {
        min-height: 100px; } }
  .main_menu_list > li.active > a {
    color: var(--bs-primary); }
  .main_menu_list > li:hover > a {
    color: var(--bs-primary); }
    .main_menu_list > li:hover > a:after {
      transform: rotateX(-180deg); }
  .main_menu_list .dropdown > a {
    position: relative; }
    .main_menu_list .dropdown > a:after {
      float: right;
      font-size: 14px;
      content: "\f107";
      font-weight: 900;
      margin: 1px 0 0 3px;
      transition: var(--bs-transition);
      font-family: "Font Awesome 6 Pro"; }
  .main_menu_list .dropdown-menu {
    min-width: 240px; }
    .main_menu_list .dropdown-menu .dropdown > a:after {
      margin-top: -2px;
      content: '\f105'; }
    .main_menu_list .dropdown-menu .dropdown-menu {
      top: 0;
      margin: 0;
      left: 100%; }
  .main_menu_list .dropdown-menu.mega_menu_wrapper {
    margin: 0 !important; }

@media (min-width: 992px) {
  .site_header_3 .main_menu_list > li > a {
    min-height: 54px; } }
.stricked-menu .main_menu_list > li > a {
  min-height: 90px; }

.site_header_1 .stricked-menu .dropdown-menu.mega_menu_wrapper {
  top: 89px; }

.site_header_1 .header_bottom > .container > .row > [class*="col-"] {
  padding: 0 15px; }

.dropdown:has(.mega_menu_wrapper) {
  position: static; }

.mega_menu_wrapper {
  left: 0;
  right: 0;
  border: none;
  padding: 60px 0;
  position: fixed;
  border-radius: 0px;
  transform: translate(0%, 4px);
  box-shadow: -2px 24px 52px 0 rgba(0, 0, 0, 0.1); }
  .mega_menu_wrapper .megamenu_pages_wrapper {
    margin: -15px; }
  .mega_menu_wrapper .site_author {
    margin: -60px 0;
    padding: 60px 50px 50px; }
  .mega_menu_wrapper .author_box .author_designation {
    opacity: 0.8; }
  .mega_menu_wrapper .site_author p {
    font-size: 18px;
    font-weight: 500;
    margin: 29px 0 0;
    line-height: 28px;
    font-family: 'Axiforma Medium'; }
  .mega_menu_wrapper .social_area {
    display: flex;
    padding: 36px 0;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid var(--bs-light); }
  .mega_menu_wrapper .social_icons_block {
    gap: 20px; }
    .mega_menu_wrapper .social_icons_block:before {
      line-height: 1;
      font-size: 14px;
      font-weight: 700;
      color: var(--bs-dark);
      display: inline-block;
      content: attr(data-text);
      font-family: 'Axiforma Bold'; }
    .mega_menu_wrapper .social_icons_block a {
      width: auto;
      height: auto;
      border: none;
      background: none;
      color: var(--bs-body-color); }
      .mega_menu_wrapper .social_icons_block a:hover {
        color: var(--bs-primary); }
      .mega_menu_wrapper .social_icons_block a [class*="fa-facebook"] {
        color: #3D6AD6; }
      .mega_menu_wrapper .social_icons_block a [class*="fa-twitter"] {
        color: #000000; }
      .mega_menu_wrapper .social_icons_block a [class*="fa-linkedin"] {
        color: #0073B1; }
      .mega_menu_wrapper .social_icons_block a [class*="fa-dribbble"] {
        color: #D31F61; }
  .mega_menu_wrapper .row:has(> [class*="col-"] > .megamenu_widget) {
    margin: 0 -60px; }
    .mega_menu_wrapper .row:has(> [class*="col-"] > .megamenu_widget) > [class*="col-"] {
      padding: 58px 60px 51px; }
      .mega_menu_wrapper .row:has(> [class*="col-"] > .megamenu_widget) > [class*="col-"]:not(:last-child) {
        border-style: solid;
        border-width: 0 1px 0 0;
        border-color: var(--bs-light); }
  .mega_menu_wrapper .megamenu_info_title {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 28px;
    color: var(--bs-body-color); }
  .mega_menu_wrapper .megamenu_widget ul {
    gap: 22px; }
    .mega_menu_wrapper .megamenu_widget ul li {
      line-height: 1;
      font-size: 20px;
      font-weight: 600;
      font-family: 'Axiforma SemiBold'; }
      .mega_menu_wrapper .megamenu_widget ul li a {
        color: var(--bs-dark); }
        .mega_menu_wrapper .megamenu_widget ul li a:hover {
          color: var(--bs-primary); }

@media screen and (min-width: 992px) {
  .dropdown:hover > .mega_menu_wrapper {
    transform: translate(0%, 0px); } }
.megamenu_case {
  padding: 50px; }
  .megamenu_case h3 {
    line-height: 1;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0;
    color: var(--bs-white);
    text-transform: uppercase;
    font-family: 'Axiforma Medium'; }
  .megamenu_case h4 {
    line-height: 1;
    font-size: 30px;
    font-weight: 700;
    margin: 10px 0 0;
    color: var(--bs-white);
    font-family: 'Axiforma SemiBold'; }
  .megamenu_case img {
    margin: 30px 0;
    display: block; }
  .megamenu_case .btn {
    box-shadow: none;
    color: var(--bs-dark);
    background-color: var(--bs-white); }

.site_header_1:before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  content: '';
  position: absolute;
  animation: hueRotate 6s linear infinite;
  background: linear-gradient(45deg, #FAEC60, #F3A338, #F37528, #F44380, #BE3DB3, #0044EB, #5A71F1, #439EFF); }
.site_header_1 + main {
  padding-top: 135px; }
.site_header_1 .container {
  max-width: 1750px; }
.site_header_1 .header_top {
  padding: 8px 0 6px; }
  .site_header_1 .header_top p {
    font-size: 14px;
    color: var(--bs-white); }
    .site_header_1 .header_top p a {
      gap: 6px;
      align-items: center;
      display: inline-flex;
      color: var(--bs-white); }
      .site_header_1 .header_top p a:hover {
        opacity: 0.7; }
.site_header_1 .header_bottom {
  padding: 14px 0;
  border-radius: 40px 40px 0 0;
  background-color: var(--bs-white);
  border-bottom: 1px solid var(--bs-border-color); }
  @media screen and (max-width: 991px) {
    .site_header_1 .header_bottom {
      padding: 35px 0; } }
  .site_header_1 .header_bottom .main_menu {
    padding: 0; }
  .site_header_1 .header_bottom.stricked-menu {
    border-radius: 0; }
.site_header_1 .dropdown-menu {
  margin-top: 0; }
  .site_header_1 .dropdown-menu:before {
    top: -46px;
    height: 46px; }
.site_header_1 .dropdown-menu.mega_menu_wrapper {
  top: 134px; }
.site_header_1.sticky .header_top {
  display: none; }
.site_header_1.sticky .header_bottom {
  padding: 15px 0;
  border-radius: 0px; }
.site_header_1.sticky .dropdown-menu {
  margin-top: 33px; }
.site_header_1.sticky .dropdown-menu:before {
  top: -37px;
  height: 36px; }
.site_header_1.sticky .dropdown-menu.mega_menu_wrapper {
  top: 82px; }

.site_header_2 {
  padding: 22px 0; }
  .site_header_2 .stricked-menu {
    padding: 10px 0;
    background-color: #020842;
    box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3); }
  .site_header_2 .container {
    max-width: 1630px; }
  .site_header_2 .main_menu_list {
    gap: 2px; }
  .site_header_2 .main_menu_list > li > a {
    padding: 12px 20px;
    border-radius: 50px;
    border: 1px solid transparent; }
  .site_header_2 .main_menu_list > li:hover > a, .site_header_2 .main_menu_list > li.active > a {
    border-color: var(--bs-border-color); }
  .site_header_2:not(.sticky) .main_menu_list > li > a {
    color: var(--bs-white); }
  .site_header_2:not(.sticky) .main_menu_list > li:hover > a, .site_header_2:not(.sticky) .main_menu_list > li.active > a {
    border-color: #1B2155; }
  .site_header_2 .dropdown-menu {
    z-index: 1; }
  .site_header_2 .site_logo .site_link img:nth-child(2) {
    display: none; }
  .site_header_2 .stricked-menu .site_logo .site_link img:nth-child(1) {
    display: none; }
  .site_header_2 .stricked-menu .site_logo .site_link img:nth-child(2) {
    display: block;width: 73px; }
  .site_header_2 .stricked-menu .main_menu_list > li > a {
    color: #ffffff; }
  .site_header_2 .stricked-menu .main_menu_list > li:hover > a, .site_header_2 .stricked-menu .main_menu_list > li.active > a {
    border-color: var(--bs-border-color);
    color: var(--bs-primary); }
  .site_header_2 .main_menu_list > li > a {
    min-height: auto; }
  .site_header_2 .dropdown-menu {
    margin-top: 19px; }
  .site_header_2 .dropdown-menu.mega_menu_wrapper {
    top: 92px; }
  .site_header_2.sticky .dropdown-menu {
    margin-top: 17px; }
  .site_header_2.sticky .dropdown-menu:before {
    top: -18px;
    height: 17px; }
  .site_header_2.sticky .dropdown-menu.mega_menu_wrapper {
    top: 77px; }

.site_header_2 .stricked-menu .dropdown-menu.mega_menu_wrapper {
  top: 76px; }

.site_header_2 .stricked-menu .dropdown-menu {
  margin-top: 18px; }

.site_header_3 {
  padding: 63px 0; }
  .site_header_3.sticky {
    padding: 10px 0; }
  .site_header_3 .container {
    max-width: 1730px; }
  .site_header_3 .main_menu_list {
    gap: 40px;
    border-radius: 50px;
    padding: 0 60px;
    background-color: var(--bs-white);
    box-shadow: 0 4px 4px 0 rgba(4, 9, 20, 0.02); }
  .site_header_3.sticky .main_menu_list {
    box-shadow: 0 4px 4px 0 rgba(4, 9, 20, 0.08); }
  .site_header_3 .btn-light:not(:hover) {
    background-color: var(--bs-white); }
  .site_header_3 .dropdown-menu {
    margin-top: 0; }
  .site_header_3 .dropdown-menu:before {
    top: -23px;
    height: 23px; }
  .site_header_3.sticky .dropdown-menu.mega_menu_wrapper {
    top: 72px; }
  .site_header_3 .main_menu {
    padding: 0; }
  .site_header_3 .xb-header-inner > .container > .row > [class*="col-"] {
    padding: 0 15px; }
  .site_header_3 .stricked-menu {
    padding: 30px 0; }
    .site_header_3 .stricked-menu .main_menu_list {
      background-color: transparent;
      box-shadow: none !important; }
    .site_header_3 .stricked-menu .main_menu_list > li > a {
      min-height: 50px; }
    .site_header_3 .stricked-menu .dropdown-menu {
      margin-top: 14px !important; }

.stricked-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  top: 0;
  -webkit-transform: translateY(-100%);
  -khtml-transform: translateY(-100%);
  -moz-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  -o-transform: translateY(-100%);
  transform: translateY(-100%);
  -webkit-transition: 0.5s;
  -khtml-transition: 0.5s;
  -moz-transition: 0.5s;
  -ms-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  visibility: hidden;
  background-color: #fff; }
  .stricked-menu .main-menu ul li a {
    padding: 30px 0; }

.stricked-menu.stricky-fixed {
  -webkit-transform: translateY(0%);
  -khtml-transform: translateY(0%);
  -moz-transform: translateY(0%);
  -ms-transform: translateY(0%);
  -o-transform: translateY(0%);
  transform: translateY(0%);
  visibility: visible;
  -webkit-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  -khtml-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  -moz-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  -ms-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  -o-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09); }

.mobile_menu_btn {
  width: 50px;
  height: 50px;
  font-size: 18px;
  border-radius: 100%;
  align-items: center;
  display: inline-flex;
  color: var(--bs-dark);
  justify-content: center;
  border: 1px solid var(--bs-light); }
  .mobile_menu_btn:hover {
    color: var(--bs-white);
    border-color: var(--bs-primary);
    background-color: var(--bs-primary); }

@media screen and (max-width: 991px) {
  .site_header .main_menu {
    left: 0;
    right: 0;
    top: 135px;
    padding: 0;
    z-index: 999;
    position: fixed; }

  .site_header.sticky .main_menu {
    top: 82px; }

  .main_menu_inner {
    padding: 0 15px; }

  .main_menu_list > li {
    width: 100%;
    display: block; }

  .main_menu_list {
    margin: auto;
    padding: 15px;
    max-width: 700px;
    border-radius: 10px;
    background-color: var(--bs-white);
    box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.12); }

  .main_menu_list > li > a {
    width: 100%;
    display: block; }

  .main_menu_list .dropdown-menu {
    position: static;
    box-shadow: none; }

  .main_menu_list > li > a {
    font-size: 16px;
    line-height: 20px;
    padding: 12px 20px; } }
/* 3.01 - Site Header - End
================================================== */
/* 3.02 - Site Footer - Start
================================================== */
.diract_contact_links {
  display: flex;
  align-items: flex-start;
  justify-content: space-between; }
  .diract_contact_links .iconbox_block.layout_icon_left {
    padding: 0;
    box-shadow: none;
    transform: unset;
    border-radius: 0;
    background-color: transparent; }
    .diract_contact_links .iconbox_block.layout_icon_left .iconbox_icon {
      width: 56px;
      height: 56px;
      font-size: 24px;
      margin: 0 20px 0 0;
      border-radius: 100%; }
      .diract_contact_links .iconbox_block.layout_icon_left .iconbox_icon img {
        max-width: 24px; }
    .diract_contact_links .iconbox_block.layout_icon_left .iconbox_title {
      font-size: 15px;
      font-weight: 500;
      line-height: 17px;
      margin-bottom: 8px;
      color: var(--bs-body-color);
      font-family: 'Axiforma Medium'; }
    .diract_contact_links .iconbox_block.layout_icon_left p {
      font-size: 20px;
      font-weight: 700;
      color: var(--bs-dark);
      font-family: 'Axiforma Bold'; }

.diract_contact_links.text-white .iconbox_block.layout_icon_left .iconbox_icon {
  color: var(--bs-white);
  background-color: #1B2155; }
.diract_contact_links.text-white .iconbox_block.layout_icon_left .iconbox_title {
  color: #B6B8CA; }
.diract_contact_links.text-white .iconbox_block.layout_icon_left p {
  color: var(--bs-white); }

.site_footer p {
  font-size: 16px;
  line-height: 26px;
  margin-bottom: 30px; }
.site_footer .icon_list.unordered_list_block {
  gap: 12px; }
.site_footer .icon_list > li {
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
  letter-spacing: 1px;
  font-family: 'Axiforma SemiBold'; }
  .site_footer .icon_list > li > a:hover {
    text-decoration: underline;
    text-decoration-thickness: 1px; }
.site_footer .icon_list.unordered_list {
  gap: 20px; }
  .site_footer .icon_list.unordered_list > li {
    font-size: 16px;
    font-weight: 500;
    font-family: 'Axiforma Medium'; }
    .site_footer .icon_list.unordered_list > li:first-child .icon_list_icon {
      display: none; }
  .site_footer .icon_list.unordered_list a {
    gap: 20px; }
.site_footer .copyright_text {
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
  font-family: 'Axiforma Medium'; }
  .site_footer .copyright_text a {
    color: currentColor; }
    .site_footer .copyright_text a:hover {
      text-decoration: underline; }

.footer_info_title {
  color: #B6B8CA;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 34px; }

.footer_newslatter {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #1B2155; }
  .footer_newslatter input {
    flex: 1;
    border: none;
    background-color: transparent; }
  .footer_newslatter button {
    color: var(--bs-dark); }
    .footer_newslatter button:hover {
      opacity: 0.8; }

.footer_newslatter_2 {
  position: relative;
  margin-bottom: 40px; }
  .footer_newslatter_2 label {
    top: 19px;
    left: 22px;
    position: absolute; }
  .footer_newslatter_2 input {
    width: 100%;
    height: 60px;
    outline: none;
    display: block;
    border-radius: 50px;
    color: var(--bs-white);
    padding: 4px 20px 0 46px;
    background-color: #1B2155;
    border: 1px solid #1B2155;
    transition: var(--bs-transition); }
    .footer_newslatter_2 input:focus {
      border-color: var(--bs-primary); }
    .footer_newslatter_2 input::placeholder {
      color: #B6B8CA; }
    .footer_newslatter_2 input:-ms-input-placeholder {
      color: #B6B8CA; }
    .footer_newslatter_2 input::-ms-input-placeholder {
      color: #B6B8CA; }
  .footer_newslatter_2 button {
    top: 10px;
    right: 10px;
    line-height: 1;
    font-size: 14px;
    font-weight: 600;
    position: absolute;
    border-radius: 50px;
    letter-spacing: 1px;
    color: var(--bs-white);
    padding: 14px 30px 12px;
    text-transform: uppercase;
    font-family: 'Axiforma SemiBold';
    background-color: var(--bs-primary); }

.footer_layout_1 p {
  color: #B6B8CA; }
.footer_layout_1 .content_box {
  z-index: 2;
  position: relative;
  margin-bottom: -40px;
  background-size: auto;
  border-radius: 0 0 40px 40px;
  background-repeat: no-repeat;
  background-color: var(--bs-dark);
  background-position: center center; }
.footer_layout_1 .diract_contact_links {
  padding: 80px 0;
  border-bottom: 1px solid #1B2155; }
.footer_layout_1 .footer_bottom {
  z-index: 1;
  position: relative;
  padding: 59px 0 14px; }
  .footer_layout_1 .footer_bottom:before {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    content: "";
    position: absolute;
    animation: hueRotate 6s linear infinite;
    background: linear-gradient(45deg, #FAEC60, #F3A338, #F37528, #F44380, #BE3DB3, #0044EB, #5A71F1, #439EFF); }
.footer_layout_1 .footer_main_content {
  padding: 100px 0; }
.footer_layout_1 .icon_list > li a {
  color: var(--bs-white); }
.footer_layout_1 .copyright_text {
  color: var(--bs-white); }
  .footer_layout_1 .copyright_text a {
    color: currentColor; }
    .footer_layout_1 .copyright_text a:hover {
      text-decoration: underline; }
.footer_layout_1 .footer_newslatter input {
  padding: 0 10px;
  color: var(--bs-white); }
  .footer_layout_1 .footer_newslatter input::placeholder {
    color: #B6B8CA; }
  .footer_layout_1 .footer_newslatter input:-ms-input-placeholder {
    color: #B6B8CA; }
  .footer_layout_1 .footer_newslatter input::-ms-input-placeholder {
    color: #B6B8CA; }
.footer_layout_1 .footer_newslatter button {
  color: var(--bs-white); }
.footer_layout_1 .social_links_block {
  margin-top: 30px; }
  .footer_layout_1 .social_links_block a {
    color: var(--bs-white);
    border: 1px solid #1B2155; }
    .footer_layout_1 .social_links_block a:hover {
      color: var(--bs-white);
      border-color: var(--bs-primary); }

.footer_layout_2 {
  padding: 150px 0 0;
  background-size: auto;
  background-repeat: no-repeat;
  background-color: var(--bs-dark);
  background-position: center center; }
  .footer_layout_2 p {
    color: #B6B8CA; }
  .footer_layout_2 .shape_image_1 {
    top: 0;
    left: 0;
    right: 0;
    text-align: center; }
    .footer_layout_2 .shape_image_1 img {
      width: calc(100% - 530px); }
  .footer_layout_2 .service_pill_carousel {
    margin-bottom: 98px; }
    .footer_layout_2 .service_pill_carousel:before {
      background-image: linear-gradient(90deg, var(--bs-dark), transparent); }
    .footer_layout_2 .service_pill_carousel:after {
      background-image: linear-gradient(90deg, transparent, var(--bs-dark)); }
  .footer_layout_2 .service_pill_block {
    border-color: #1B2155;
    color: var(--bs-white); }
    .footer_layout_2 .service_pill_block i {
      background-color: #282D5E; }
  .footer_layout_2 .icon_list a {
    color: var(--bs-white); }
  .footer_layout_2 .social_icons_block a {
    border-radius: 100%;
    border-color: #1B2155;
    color: var(--bs-white);
    background-color: transparent; }
    .footer_layout_2 .social_icons_block a:hover {
      border-color: var(--bs-primary);
      background-color: var(--bs-primary); }
  .footer_layout_2 .footer_bottom {
    margin-top: 92px;
    padding: 13px 0 9px;
    background-repeat: no-repeat;
    background-size: 1390px auto;
    background-position: center top; }
    .footer_layout_2 .footer_bottom p {
      color: var(--bs-dark); }
    .footer_layout_2 .footer_bottom .icon_list a {
      color: var(--bs-dark); }
      .footer_layout_2 .footer_bottom .icon_list a:hover {
        color: var(--bs-primary); }

.footer_layout_3 .footer_main_content {
  padding: 112px 0 92px; }
.footer_layout_3 .footer_info_title {
  line-height: 1;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.5);
  font-family: 'Axiforma Medium'; }
.footer_layout_3 .icon_list > li {
  font-weight: 500;
  line-height: 32px;
  font-family: 'Axiforma Medium'; }
  .footer_layout_3 .icon_list > li > a {
    color: var(--bs-white); }
.footer_layout_3 .footer_bottom {
  padding: 7px 0; }
.footer_layout_3 .social_wrap {
  gap: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end; }
  .footer_layout_3 .social_wrap .footer_info_title {
    gap: 10px;
    position: relative;
    align-items: center;
    display: inline-flex; }
    .footer_layout_3 .social_wrap .footer_info_title:after {
      height: 1px;
      width: 48px;
      content: '';
      display: inline-block;
      background-color: var(--bs-white); }
  .footer_layout_3 .social_wrap .social_icons_block a {
    border-radius: 100%;
    color: var(--bs-dark);
    border-color: var(--bs-white); }
    .footer_layout_3 .social_wrap .social_icons_block a:hover {
      color: var(--bs-white);
      background-color: transparent;
      border-color: rgba(255, 255, 255, 0.2); }

/* 3.02 - Site Footer - End
================================================== */
/* 3.03 - Page Header, Page Banner, Breadcrumb - Start
================================================== */
.page_banner_section {
  z-index: 1;
  position: relative;
  padding: 130px 0 110px;
  background-size: cover;
  background-repeat: no-repeat;
  background-color: var(--bs-dark);
  background-position: center center; }
  .page_banner_section .heading_focus_text {
    margin-bottom: 24px; }

.page_title {
  font-size: 55px;
  line-height: 70px; }

/* 3.03 - Page Header, Page Banner, Breadcrumb - End
================================================== */
/* 3.04 - Sidebar - Start
================================================== */
.sidebar > *:not(:last-child) {
  margin-bottom: 70px; }

.sidebar_widget_title {
  line-height: 1;
  font-size: 18px;
  margin-bottom: 35px;
  text-transform: uppercase; }

/* 3.04 - Sidebar - End
================================================== */
/* 4.01 - Blog - Start
================================================== */
.blog_section_space {
  padding-top: 125px;
  padding-bottom: 75px; }

.blog_section {
  background-size: cover;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-position: center center; }
  .blog_section .shape_image_1 {
    right: 0;
    bottom: 0; }
  .blog_section .shape_image_2 {
    left: 0;
    bottom: 0;
    max-width: 265px; }

.blog_post_block {
  overflow: hidden;
  transition: var(--bs-transition);
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius-sm);
  box-shadow: 0 1px 2px 0 rgba(174, 191, 210, 0.3); }
  .blog_post_block:hover {
    box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }
  .blog_post_block .blog_post_image .image_wrap {
    display: block;
    overflow: hidden;
    position: relative; }
    .blog_post_block .blog_post_image .image_wrap img {
      transition: var(--bs-transition); }
    .blog_post_block .blog_post_image .image_wrap:hover img {
      transform: scale(1.108); }
    .blog_post_block .blog_post_image .image_wrap:has(i):before {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0;
      z-index: 1;
      content: '';
      transition: .2s;
      position: absolute;
      background-color: rgba(2, 8, 66, 0.4); }
  .blog_post_block .blog_post_image i {
    top: 50%;
    left: 50%;
    z-index: 2;
    width: 100px;
    height: 100px;
    font-size: 30px;
    position: absolute;
    align-items: center;
    border-radius: 100%;
    display: inline-flex;
    color: var(--bs-white);
    justify-content: center;
    background-color: var(--bs-primary);
    transform: translate(-50%, -50%) scale(0);
    transition: transform var(--bs-transition); }
  .blog_post_block:hover .image_wrap:before {
    opacity: 1 !important; }
  .blog_post_block:hover .image_wrap i {
    transform: translate(-50%, -50%) scale(1); }
  .blog_post_block .blog_post_content {
    padding: 40px 30px; }
  .blog_post_block .post_meta_wrap {
    gap: 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start; }
    .blog_post_block .post_meta_wrap .category_btns_group {
      gap: 3px; }
    .blog_post_block .post_meta_wrap .post_meta {
      margin-top: 4px; }
  .blog_post_block .blog_post_title {
    font-size: 22px;
    font-weight: 600;
    line-height: 32px;
    margin: 24px 0 30px;
    letter-spacing: -0.3px;
    font-family: 'Axiforma SemiBold'; }
    .blog_post_block .blog_post_title a {
      color: var(--bs-dark); }
      .blog_post_block .blog_post_title a:hover {
        color: var(--bs-primary); }

.blog_post_block.layout_2 {
  box-shadow: none;
  border-radius: 0;
  background-color: transparent; }
  .blog_post_block.layout_2 .blog_post_image .image_wrap {
    border-radius: var(--bs-border-radius); }
  .blog_post_block.layout_2 .post_meta {
    gap: 30px;
    margin: 15px 0 0;
    padding: 20px 0 0;
    border-top: 1px solid var(--bs-border-color); }

.blog_post_block.image_left_layout {
  display: flex;
  align-items: center; }
  .blog_post_block.image_left_layout:not(:last-child) {
    margin-bottom: 30px; }
  .blog_post_block.image_left_layout .blog_post_image {
    width: 312px;
    flex: 0 0 auto; }
  .blog_post_block.image_left_layout .blog_post_content {
    flex: 1;
    padding: 40px; }
  .blog_post_block.image_left_layout .blog_post_title {
    font-size: 26px;
    line-height: 35px;
    margin: 20px 0 14px; }
  .blog_post_block.image_left_layout p {
    margin-bottom: 30px; }
  .blog_post_block.image_left_layout .btn {
    padding: 0 26px; }
    .blog_post_block.image_left_layout .btn .btn_label {
      padding: 15px 0px 13px; }

.blog_onecol_carousel {
  position: relative; }
  .blog_onecol_carousel [class*="b1cc-swiper-button-"] {
    top: 50%;
    z-index: 2;
    width: 28px;
    display: flex;
    height: 158px;
    text-align: center;
    position: absolute;
    align-items: center;
    justify-content: center;
    background-size: 100% 100%;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-position: center center; }
    .blog_onecol_carousel [class*="b1cc-swiper-button-"]:hover {
      color: var(--bs-primary); }
  .blog_onecol_carousel .b1cc-swiper-button-prev {
    left: -1px; }
  .blog_onecol_carousel .b1cc-swiper-button-next {
    right: -1px; }
  .blog_onecol_carousel .b1cc-swiper-pagination {
    right: 50px;
    bottom: 50px;
    left: auto;
    z-index: 1;
    width: auto;
    position: absolute; }
    .blog_onecol_carousel .b1cc-swiper-pagination .swiper-pagination-bullet {
      background-color: var(--bs-white); }

.blog_post_block.content_over_layout {
  position: relative; }
  .blog_post_block.content_over_layout .image_wrap:before {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    content: '';
    position: absolute;
    background-image: linear-gradient(0deg, var(--bs-primary), transparent); }
  .blog_post_block.content_over_layout .blog_post_content {
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    position: absolute;
    padding: 50px 50px 40px; }
  .blog_post_block.content_over_layout .blog_post_title {
    font-size: 45px;
    line-height: 58px;
    margin: 24px 0 15px; }
    .blog_post_block.content_over_layout .blog_post_title a {
      color: var(--bs-white); }
      .blog_post_block.content_over_layout .blog_post_title a:hover {
        opacity: 0.8; }
  .blog_post_block.content_over_layout p {
    font-size: 16px;
    max-width: 699px;
    line-height: 28px;
    color: var(--bs-white); }
  .blog_post_block.content_over_layout .category_btns_group a {
    border-color: var(--bs-white);
    background-color: var(--bs-white); }
    .blog_post_block.content_over_layout .category_btns_group a:hover {
      color: var(--bs-primary); }
  .blog_post_block.content_over_layout .post_meta > li a {
    color: var(--bs-white); }
    .blog_post_block.content_over_layout .post_meta > li a i {
      color: var(--bs-white); }

.blog_section_2 {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center; }

.blog_post_block_2 {
  display: flex;
  position: relative;
  align-items: center; }
  .blog_post_block_2 .blog_post_image {
    order: 1; }
    .blog_post_block_2 .blog_post_image > a {
      display: block;
      overflow: hidden;
      position: relative;
      border-radius: 32px; }
    .blog_post_block_2 .blog_post_image img {
      transition: var(--bs-transition); }
  .blog_post_block_2 .blog_post_content {
    z-index: 1;
    max-width: 340px;
    position: relative;
    margin: 0 -180px 0 0;
    border-radius: 16px;
    padding: 60px 30px 52px;
    background-color: var(--bs-white);
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.05); }
  .blog_post_block_2 .category_list > li {
    font-size: 14px;
    font-weight: 700;
    font-family: 'Axiforma Bold'; }
    .blog_post_block_2 .category_list > li a:not(:hover) {
      color: var(--bs-dark); }
  .blog_post_block_2 .post_title {
    font-size: 30px;
    line-height: 38px;
    margin: 22px 0 14px; }
    .blog_post_block_2 .post_title a {
      color: var(--bs-dark); }
      .blog_post_block_2 .post_title a:hover {
        text-decoration: underline;
        text-decoration-thickness: 2px; }
  .blog_post_block_2 .post_meta {
    row-gap: 6px; }
  .blog_post_block_2:hover .blog_post_image img {
    transform: scale(1.08); }
  .blog_post_block_2:hover .post_title > a {
    color: var(--bs-primary); }

.post_list_block > ul {
  gap: 20px; }
  .post_list_block > ul > li {
    padding: 26px 30px 22px;
    background-color: var(--bs-white);
    border-radius: var(--bs-border-radius);
    box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }
    .post_list_block > ul > li .post_title {
      font-size: 18px;
      line-height: 26px;
      margin-bottom: 16px; }
      .post_list_block > ul > li .post_title > a {
        color: var(--bs-dark); }
        .post_list_block > ul > li .post_title > a:hover {
          color: var(--bs-primary); }
    .post_list_block > ul > li:hover .post_title > a {
      text-decoration: underline; }

/* 4.01 - Blog - End
================================================== */
/* 4.02 - Call To Action - Start
================================================== */
.calltoaction_section {
  padding: 100px 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-blend-mode: overlay;
  background-position: center center;
  background-color: rgba(2, 8, 66, 0.8); }
  .calltoaction_section .heading_block .heading_text {
    font-size: 54px; }
  .calltoaction_section:has(.instant_contact_form) {
    background-blend-mode: unset; }

/* 4.02 - Call To Action - End
================================================== */
/* 4.03 - Case - Start
================================================== */
.case_study_block {
  display: flex;
  overflow: hidden;
  align-items: center;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius);
  position: sticky;
  top: 100px;
  box-shadow: 0 4px 23px 0 rgba(2, 9, 63, 0.16); }
  .case_study_block:not(:last-child) {
    margin-bottom: 30px; }
  .case_study_block .case_study_image {
    order: 1;
    flex: 0 0 auto;
    max-width: 465px; }
  .case_study_block .case_study_content {
    padding: 50px 140px 50px 80px; }
  .case_study_block .case_title {
    font-size: 30px;
    line-height: 40px;
    margin: 16px 0 24px; }
    .case_study_block .case_title a {
      color: var(--bs-dark); }
      .case_study_block .case_title a:hover {
        color: var(--bs-primary); }
  .case_study_block p {
    margin-bottom: 20px; }
  .case_study_block .icon_list {
    gap: 10px 50px;
    margin-bottom: 30px; }
  .case_study_block .case_technologies {
    gap: 20px;
    margin-bottom: 26px; }
    .case_study_block .case_technologies:before {
      font-size: 18px;
      font-weight: 500;
      margin-right: 40px;
      color: var(--bs-dark);
      display: inline-block;
      content: attr(data-text);
      font-family: 'Axiforma Medium'; }
    .case_study_block .case_technologies > li {
      width: 58px;
      height: 58px;
      flex: 0 0 auto;
      border-radius: 100%;
      align-items: center;
      display: inline-flex;
      justify-content: center;
      background-color: var(--bs-white);
      box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3); }
      .case_study_block .case_technologies > li img {
        max-width: 30px; }

/* 4.03 - Case - End
================================================== */
/* 4.04 - Client Logo - Start
================================================== */
.client_logo_section {
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-color: var(--bs-light);
  background-position: center center; }

.client_logo_carousel .swiper-wrapper {
  margin: -30px -5px;
  pointer-events: none;
  transition-timing-function: linear !important; }
  .client_logo_carousel .swiper-wrapper .swiper-slide {
    padding: 30px 5px; }

.client_logo_item {
  height: 118px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #E4EEF0;
  transition: var(--bs-transition);
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius); }
  .client_logo_item:hover {
    box-shadow: 0 8px 15px 0 rgba(3, 11, 76, 0.08); }
  .client_logo_item img {
    max-width: 124px; }

.no_style .client_logo_item {
  padding: 0;
  border: none;
  height: 60px;
  border-radius: 0;
  box-shadow: none;
  background-color: transparent; }

.feature_partners_section {
  padding: 70px 0;
  padding-bottom: 20px;
  background-color: #0132B8;
  overflow: hidden;
  position: relative; }
  .feature_partners_section::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 22%;
    height: 100%;
    content: "";
    background-color: #0132B8;
    z-index: 2; }
  .feature_partners_section .title_text {
    top: -40px;
    left: 5px;
    bottom: -40px;
    z-index: 2;
    padding: 0 40px;
    font-size: 20px;
    font-weight: 600;
    max-width: 240px;
    position: absolute;
    align-items: center;
    display: inline-flex;
    justify-content: center;
    background-color: #0132B8;
    font-family: 'Axiforma SemiBold'; }
  .feature_partners_section .client_logo_carousel .swiper-wrapper .swiper-slide {
    padding: 30px 8px; }
  .feature_partners_section .client_logo_item {
    height: 70px;
    border: none;
    display: flex;
    border-radius: 5px;
    align-items: center;
    clip-path: polygon(84% 0, 100% 40%, 100% 100%, 0 100%, 0 0); }
    .feature_partners_section .client_logo_item img {
      max-width: 90px; }

/* 4.04 - Client Logo - End
================================================== */
/* 4.05 - Hero Sections - Start
================================================== */
.it_solution_hero_section {
  padding: 70px 0; }
  @media screen and (max-width: 991px) {
    .it_solution_hero_section {
      padding-top: 20px; } }
  .it_solution_hero_section .container {
    max-width: 1750px; }

.it_solution_hero_content {
  background-size: auto;
  padding: 132px 220px 133px;
  background-repeat: no-repeat;
  background-position: left center;
  background-color: var(--bs-light);
  border-radius: var(--bs-border-radius-sm); }
  .it_solution_hero_content h1 {
    font-size: 55px;
    line-height: 70px;
    margin: 15px 0 20px;
    letter-spacing: -2px; }
  .it_solution_hero_content p {
    font-size: 18px;
    line-height: 28px;
    margin-bottom: 51px; }

.review_short_info img {
  max-width: 128px; }
.review_short_info span {
  line-height: 1;
  font-size: 26px;
  font-weight: 700;
  margin-left: 15px;
  color: var(--bs-primary);
  font-family: 'Axiforma Bold'; }
.review_short_info .review_counter {
  margin-top: 6px;
  font-size: 18px; }
  .review_short_info .review_counter b {
    color: var(--bs-dark); }

.review_short_info_2 {
  display: flex;
  align-items: center;
  background-color: var(--bs-light);
  border-radius: var(--bs-border-radius);
  border: 1px solid var(--bs-border-color); }
  .review_short_info_2 .review_admin_logo {
    height: 60px;
    width: 110px;
    flex: 0 0 auto;
    align-items: center;
    display: inline-flex;
    justify-content: center;
    border-right: 1px solid var(--bs-white); }
    .review_short_info_2 .review_admin_logo img {
      max-width: 72px; }
  .review_short_info_2 .review_info_content {
    width: 190px;
    flex: 0 0 auto;
    padding: 2px 25px; }
  .review_short_info_2 .rating_block {
    margin-bottom: 8px; }
    .review_short_info_2 .rating_block > li {
      font-size: 11px; }
  .review_short_info_2 .review_counter {
    line-height: 1;
    font-size: 14px; }

.it_solution_hero_images {
  align-items: flex-start; }
  .it_solution_hero_images > li {
    width: 50%; }
  .it_solution_hero_images .worldwide_clients {
    background-color: #FB7929;
    padding: 66px 85px 65px 90px;
    border-radius: 175px 20px 0px 175px; }
    .it_solution_hero_images .worldwide_clients .counter_value {
      line-height: 1;
      font-size: 55px;
      font-weight: 700;
      margin: 0 0 12px;
      color: var(--bs-white);
      font-family: 'Axiforma Bold'; }
    .it_solution_hero_images .worldwide_clients p {
      font-size: 18px;
      line-height: 28px;
      margin-bottom: 46px;
      color: var(--bs-white); }
  .it_solution_hero_images .categories {
    padding: 115px 42px;
    border-radius: 0px 175px 0 20px;
    background-color: var(--bs-secondary); }
    .it_solution_hero_images .categories > li:not(:first-child) {
      margin-top: -1px; }
    .it_solution_hero_images .categories a {
      font-size: 15px;
      align-items: center;
      display: inline-flex;
      color: var(--bs-white); }
      .it_solution_hero_images .categories a span {
        padding: 10px 15px 6px;
        transition: var(--bs-transition);
        border: 1px solid var(--bs-white);
        border-radius: var(--bs-border-radius-pill); }
      .it_solution_hero_images .categories a i {
        width: 40px;
        height: 40px;
        flex: 0 0 auto;
        border-radius: 100%;
        align-items: center;
        display: inline-flex;
        justify-content: center;
        transition: var(--bs-transition);
        border: 1px solid var(--bs-white); }
      .it_solution_hero_images .categories a:hover span, .it_solution_hero_images .categories a:hover i {
        background-color: var(--bs-primary); }
  .it_solution_hero_images .business_growth_content {
    text-align: center;
    padding: 50px 0 96px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center; }
    .it_solution_hero_images .business_growth_content .business_growth {
      width: auto !important;
      height: auto !important; }
      .it_solution_hero_images .business_growth_content .business_growth svg {
        width: 260px;
        height: 260px; }
        .it_solution_hero_images .business_growth_content .business_growth svg circle:not(.business_growth-circle-58) {
          stroke: rgba(255, 255, 255, 0.5); }
        .it_solution_hero_images .business_growth_content .business_growth svg .business_growth-circle-58 {
          stroke-width: 8px;
          stroke: var(--bs-white); }
        .it_solution_hero_images .business_growth_content .business_growth svg .business_growth-text-58 {
          line-height: 1;
          font-size: 24px;
          font-weight: 700;
          fill: var(--bs-white);
          font-family: 'Axiforma Bold';
          transform: translateY(-10px); }
    .it_solution_hero_images .business_growth_content p {
      font-size: 14px;
      max-width: 155px;
      line-height: 22px;
      margin: -122px auto 0;
      color: var(--bs-white); }

.avatar_group > li {
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 100%;
  align-items: center;
  display: inline-flex;
  justify-content: center;
  border: 2px solid var(--bs-white); }
  .avatar_group > li:not(:first-child) {
    margin-left: -15px; }
  .avatar_group > li:last-child {
    font-size: 15px;
    color: var(--bs-white);
    background-color: var(--bs-primary); }

.software_company_hero_section {
  z-index: 1;
  position: relative;
  padding: 125px 0 125px;
  background-color: var(--bs-dark); }
  .software_company_hero_section h1 {
    font-size: 56px;
    line-height: 70px;
    margin-bottom: 10px; }
  .software_company_hero_section p {
    color: #B6B8CA;
    font-size: 16px;
    max-width: 527px;
    margin: 0 0 37px;
    line-height: 28px; }
  .software_company_hero_section [class*="shape_image_"] {
    position: absolute; }
  .software_company_hero_section .shape_image_1 {
    top: 90px;
    left: 152px;
    max-width: 1130px; }
  .software_company_hero_section .shape_image_2 {
    left: 152px;
    bottom: 23px;
    max-width: 1130px; }
  .software_company_hero_section .shape_image_3 {
    top: 99px;
    right: -50px;
    max-width: 328px; }
  .software_company_hero_section .shape_image_4 {
    right: -50px;
    bottom: 54px;
    max-width: 328px; }

.engine_image {
  float: right;
  width: 695px;
  height: 695px;
  position: relative;
  margin: 0 -210px 0 0;
  transform: translateY(30px); }
  .engine_image [class*="image_wrap_"] {
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    display: flex;
    position: absolute;
    transform: translate(-50%, -50%); }
    .engine_image [class*="image_wrap_"] img {
      margin: auto; }
  .engine_image .image_wrap_1 {
    z-index: 4;
    max-width: 153px; }
  .engine_image .image_wrap_2 {
    z-index: 3;
    max-width: 448px; }
    .engine_image .image_wrap_2 img {
      animation: spin 40s infinite linear; }
  .engine_image .image_wrap_3 {
    z-index: 2;
    max-width: 566px; }
    .engine_image .image_wrap_3 img {
      animation: spinReverse 40s infinite linear; }
  .engine_image .image_wrap_4 {
    margin: -8px 0 -1px -14px; }

.step_list {
  gap: 5px;
  padding-left: 28px;
  margin-bottom: 47px;
  border-left: 1px solid var(--bs-primary); }
  .step_list > li {
    font-size: 18px;
    font-weight: 500;
    line-height: 28px;
    position: relative;
    font-family: 'Axiforma Medium'; }
    .step_list > li:first-child {
      margin-top: -4px; }
    .step_list > li:last-child {
      margin-bottom: -5px; }
    .step_list > li:before {
      top: 0;
      left: -38px;
      font-size: 20px;
      font-weight: 400;
      content: '\f192';
      position: absolute;
      color: var(--bs-primary);
      font-family: 'Font Awesome 5 Pro'; }

.hotline_block {
  gap: 15px;
  align-items: center;
  display: inline-flex; }
  .hotline_block .hotline_icon {
    width: 60px;
    height: 60px;
    flex: 0 0 auto;
    font-size: 22px;
    border-radius: 100%;
    align-items: center;
    display: inline-flex;
    color: var(--bs-white);
    justify-content: center;
    background-color: #55D062; }
  .hotline_block .hotline_content {
    display: flex;
    flex-direction: column; }
  .hotline_block small {
    color: #B6B8CA;
    font-size: 14px;
    font-weight: 500;
    font-family: 'Axiforma Medium'; }
  .hotline_block strong {
    font-size: 18px;
    font-weight: 500;
    font-family: 'Axiforma SemiBold'; }

.business_consulting_hero_section {
  --bs-primary: #0132B8;
  --bs-primary-rgb: 1, 50, 184;
  padding: 170px 0 87px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center; }
  .business_consulting_hero_section .container {
    max-width: 1360px; }
  .business_consulting_hero_section .shape_1 {
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    max-width: 615px;
    position: absolute;
    align-items: center;
    justify-content: center; }
  .business_consulting_hero_section h1 {
    font-size: 80px;
    margin-bottom: 0;
    line-height: 95px; }
    .business_consulting_hero_section h1 u {
      text-decoration-thickness: 3px; }
  .business_consulting_hero_section p {
    font-size: 26px;
    line-height: 40px;
    margin: 3px 0 41px 0;
    color: var(--bs-dark); }
  .business_consulting_hero_section .google_reviews {
    margin-top: 55px; }

.business_consulting_hero_image {
  z-index: 1;
  padding: 0 45px;
  position: relative; }
  .business_consulting_hero_image .hero_image {
    border-radius: var(--bs-border-radius); }
  .business_consulting_hero_image .funfact_block.capsule_layout {
    z-index: 2;
    position: absolute;
    display: inline-flex; }
    .business_consulting_hero_image .funfact_block.capsule_layout:nth-child(2) {
      top: 77px;
      left: -61px; }
    .business_consulting_hero_image .funfact_block.capsule_layout:nth-child(3) {
      left: -61px;
      bottom: 85px; }
      .business_consulting_hero_image .funfact_block.capsule_layout:nth-child(3) .funfact_icon {
        background-color: #FFBE16; }
    .business_consulting_hero_image .funfact_block.capsule_layout:nth-child(4) {
      top: 45%;
      right: -61px; }

.google_reviews {
  gap: 15px;
  display: flex;
  align-items: center; }
  .google_reviews .review_admin_logo {
    flex: 0 0 auto;
    max-width: 122px; }
  .google_reviews .rating_block {
    gap: 4px; }
    .google_reviews .rating_block li {
      font-size: 15px; }
  .google_reviews .review_counter {
    font-size: 16px;
    margin-top: 4px;
    color: var(--bs-dark); }
    .google_reviews .review_counter b {
      font-size: 18px; }

/* 4.05 - Hero Sections - End
================================================== */
/* 4.06 - Policy - Start
================================================== */
.policy_section .iconbox_block.layout_icon_left {
  padding: 0;
  box-shadow: none;
  transform: unset;
  background-color: transparent; }

/* 4.06 - Policy - End
================================================== */
/* 4.07 - Portfolio - Start
================================================== */
.portfolio_block {
  overflow: hidden;
  transition: var(--bs-transition);
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius-sm);
  box-shadow: 0 1px 2px 0 rgba(174, 191, 210, 0.3); }
  .portfolio_block .portfolio_image_wrap {
    display: block;
    overflow: hidden; }
  .portfolio_block .portfolio_content {
    position: relative;
    padding: 25px 30px 28px; }
  .portfolio_block .portfolio_title {
    font-size: 26px;
    line-height: 36px;
    margin-bottom: 7px; }
    .portfolio_block .portfolio_title a {
      color: var(--bs-dark); }
      .portfolio_block .portfolio_title a:hover {
        color: var(--bs-primary); }
  .portfolio_block .category_list {
    gap: 30px; }
    .portfolio_block .category_list i {
      margin-right: 2px;
      color: var(--bs-primary); }
  .portfolio_block .btn {
    top: 26px;
    right: 30px;
    position: absolute; }
  .portfolio_block:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }

.portfolio_block.portfolio_layout_2 {
  padding: 20px; }
  .portfolio_block.portfolio_layout_2 .portfolio_image_wrap {
    border-radius: var(--bs-border-radius-sm); }
    .portfolio_block.portfolio_layout_2 .portfolio_image_wrap img {
      transition: var(--bs-transition); }
    .portfolio_block.portfolio_layout_2 .portfolio_image_wrap:hover img {
      transform: scale(1.08); }
  .portfolio_block.portfolio_layout_2 .portfolio_content {
    display: flex;
    padding: 38px 40px 20px 20px;
    flex-direction: column-reverse; }
  .portfolio_block.portfolio_layout_2 .portfolio_title {
    margin: 21px 0 0; }

/* 4.07 - Portfolio - End
================================================== */
/* 4.08 - Review - Start
================================================== */
.review_section {
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center center; }

.portfolio_section:has(.portfolio_carousel) + .review_section {
  padding-top: 490px;
  margin-top: -375px; }

.review_block {
  z-index: 1;
  overflow: hidden;
  position: relative;
  text-align: center;
  padding: 45px 30px 30px;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius-sm);
  box-shadow: 0 20px 30px 0 rgba(174, 191, 210, 0.3); }
  .review_block:not(:last-child) {
    margin-bottom: 30px; }
  .review_block:before {
    opacity: 0;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    content: '';
    z-index: -1;
    position: absolute;
    transition: var(--bs-transition);
    animation: borderDashedAnimation .5s infinite linear;
    background-size: 15px 1px, 15px 1px, 1px 15px, 1px 15px;
    background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;
    background-position: left top, right bottom, left bottom, right top;
    background-image: linear-gradient(90deg, var(--bs-primary) 50%, transparent 50%), linear-gradient(90deg, var(--bs-primary) 50%, transparent 50%), linear-gradient(0deg, var(--bs-primary) 50%, transparent 50%), linear-gradient(0deg, var(--bs-primary) 50%, transparent 50%); }
  .review_block .review_title {
    font-size: 26px;
    line-height: 36px;
    margin-bottom: 18px; }
  .review_block .review_commtent {
    font-size: 18px;
    max-width: 470px;
    line-height: 32px;
    margin: 0 auto 10px; }
  .review_block .review_admin {
    z-index: 1;
    height: 430px;
    display: flex;
    margin: 0 -30px;
    padding-top: 40px;
    position: relative;
    flex-direction: column; }
    .review_block .review_admin:before {
      top: 100px;
      left: 50%;
      z-index: -1;
      content: '';
      opacity: 0.1;
      width: 486px;
      height: 486px;
      position: absolute;
      border-radius: 100%;
      transform: translateX(-50%);
      transition: var(--bs-transition);
      background-image: linear-gradient(0deg, transparent 55%, var(--bs-primary)); }
  .review_block .review_admin_image {
    max-width: 330px;
    margin: auto auto 0; }
    .review_block .review_admin_image img {
      transition: var(--bs-transition); }
  .review_block .review_admin_info {
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    position: absolute;
    padding: 100px 30px 32px;
    background-image: linear-gradient(0deg, var(--bs-white) 50%, transparent); }
  .review_block .review_admin_name {
    font-size: 22px;
    font-weight: 600;
    line-height: 32px;
    margin-bottom: 2px;
    font-family: 'Axiforma SemiBold'; }
  .review_block .review_admin_designation {
    font-size: 12px;
    letter-spacing: 2px;
    color: var(--bs-dark);
    text-transform: uppercase; }
  .review_block:hover:before {
    opacity: 0.2; }
  .review_block:hover .review_admin_image img {
    transform: scale(1.06); }
  .review_block:hover .review_admin:before {
    opacity: 0.6; }

.review_onecol_wrapper {
  padding: 47px 50px 50px;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius);
  box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3); }

.review_onecol_carousel {
  position: relative; }
  .review_onecol_carousel .carousel_arrows_nav {
    gap: 10px;
    z-index: 1;
    right: 0;
    bottom: 0;
    position: absolute;
    align-items: center;
    display: inline-flex; }
    .review_onecol_carousel .carousel_arrows_nav button {
      width: 40px;
      height: 40px;
      flex: 0 0 auto;
      align-items: center;
      border-radius: 100%;
      display: inline-flex;
      color: var(--bs-dark);
      justify-content: center;
      background-color: var(--bs-light); }
      .review_onecol_carousel .carousel_arrows_nav button:hover {
        color: var(--bs-white);
        background-color: var(--bs-primary); }

.review_block_2 .review_title {
  font-size: 30px;
  line-height: 36px;
  margin-bottom: 45px;
  color: var(--bs-primary); }
.review_block_2 .review_commtent {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 59px;
  color: var(--bs-dark);
  font-family: 'Axiforma Medium'; }
.review_block_2 .review_admin {
  gap: 30px;
  display: flex;
  align-items: center; }
.review_block_2 .review_admin_image {
  width: 107px;
  height: 127px;
  display: flex;
  flex: 0 0 auto;
  overflow: hidden;
  padding-top: 10px;
  align-items: flex-end;
  justify-content: center;
  background-color: var(--bs-light);
  border-radius: var(--bs-border-radius); }
.review_block_2 .review_admin_name {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  font-family: 'Axiforma SemiBold'; }
.review_block_2 .review_admin_designation {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 24px;
  font-family: 'Axiforma Medium'; }
.review_block_2 .review_admin_country {
  gap: 6px;
  display: flex;
  align-items: center; }
.review_block_2 .country_flag {
  width: 20px;
  height: 20px;
  flex: 0 0 auto;
  margin-top: -4px;
  overflow: hidden;
  border-radius: 100%; }
.review_block_2 .country_text {
  line-height: 1;
  font-size: 12px;
  font-weight: 500;
  color: var(--bs-dark);
  font-family: 'Axiforma Medium'; }
.review_block_2 .review_admin_logo {
  max-width: 152px; }

.review_block_3 {
  padding: 30px;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius); }
  .review_block_3 .review_commtent {
    font-size: 17px;
    font-weight: 600;
    line-height: 26px;
    margin: 25px 0 32px;
    color: var(--bs-dark);
    font-family: 'Axiforma SemiBold'; }
  .review_block_3 .review_admin {
    gap: 20px;
    display: flex;
    align-items: center; }
    .review_block_3 .review_admin .review_admin_image {
      width: 60px;
      height: 60px;
      flex: 0 0 auto;
      overflow: hidden;
      border-radius: 100%;
      background-color: var(--bs-gray); }
    .review_block_3 .review_admin .review_admin_name {
      line-height: 1;
      font-size: 17px;
      font-weight: 700;
      margin-bottom: 8px;
      font-family: "Axiforma Bold"; }
    .review_block_3 .review_admin .review_admin_designation {
      display: block;
      line-height: 1;
      font-size: 14px;
      font-weight: 500;
      font-family: "Axiforma Medium"; }

/* 4.08 - Review - End
================================================== */
/* 4.09 - Service - Start
================================================== */
.service_block {
  position: relative; }
  .service_block .service_image {
    overflow: hidden;
    position: relative;
    transition: 0.3s;
    border-radius: var(--bs-border-radius-sm); }
    .service_block .service_image img {
      transition: 0.3s; }
  .service_block:hover .service_image {
    transform: scale(0.98); }
    .service_block:hover .service_image img {
      transform: scale(1.11); }
  .service_block .service_content {
    left: 0;
    right: 0;
    bottom: 0;
    bottom: 0;
    z-index: 2;
    padding: 40px;
    position: absolute; }
  .service_block .service_title {
    font-size: 32px;
    max-width: 360px;
    line-height: 40px;
    margin-bottom: 40px; }
    .service_block .service_title a {
      display: inline;
      color: var(--bs-white);
      background-size: 0 100%;
      backface-visibility: hidden;
      background-position-y: -2px;
      background-repeat: no-repeat;
      transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
      background-image: linear-gradient(transparent calc(100% - 2px), currentColor 2px); }
      .service_block .service_title a:hover {
        background-size: 100% 100%; }
  .service_block .links_wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between; }
  .service_block .category_btns_group {
    gap: 10px; }
    .service_block .category_btns_group a {
      color: var(--bs-white);
      border-color: rgba(255, 255, 255, 0.15);
      background-color: rgba(255, 255, 255, 0.1); }
      .service_block .category_btns_group a:hover {
        color: var(--bs-dark);
        border-color: var(--bs-white);
        background-color: var(--bs-white); }
  .service_block .icon_block {
    order: -1;
    width: 50px;
    height: 50px;
    border-color: var(--bs-white);
    background-color: var(--bs-white); }
    .service_block .icon_block:hover {
      color: var(--bs-white);
      background-color: transparent;
      border-color: rgba(255, 255, 255, 0.3); }

.service_section .shape_image_1 {
  left: 0;
  top: 50%;
  max-width: 190px;
  transform: translateY(-50%); }
.service_section .shape_image_2 {
  top: 30%;
  right: 0;
  max-width: 270px; }
.service_section .shape_image_3 {
  top: 0;
  right: -10px;
  width: calc(100% - 265px); }
.service_section .shape_image_4 {
  right: 0;
  bottom: 0;
  max-width: 265px; }
.service_section .shape_image_5 {
  top: 0;
  left: 0;
  max-width: 265px; }

.service_block_2 {
  padding: 40px;
  transition: var(--bs-transition);
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius);
  box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3);
  clip-path: polygon(80% 0, 100% 20%, 100% 100%, 0 100%, 0 0); }
  .service_block_2 .service_icon {
    margin-bottom: 40px; }
  .service_block_2 .service_title {
    font-size: 26px;
    line-height: 36px;
    margin-bottom: 30px; }
    .service_block_2 .service_title a {
      color: var(--bs-dark); }
      .service_block_2 .service_title a:after {
        opacity: 0;
        content: '\f061';
        font-weight: 700;
        margin-left: 10px;
        color: var(--bs-dark);
        display: inline-block;
        transform: translateX(-5px);
        transition: var(--bs-transition);
        font-family: "Font Awesome 5 Pro"; }
      .service_block_2 .service_title a:hover {
        color: var(--bs-primary); }
        .service_block_2 .service_title a:hover:after {
          color: var(--bs-primary); }
  .service_block_2 .icon_list {
    gap: 10px; }
    .service_block_2 .icon_list > li {
      line-height: 1;
      border-radius: 5px;
      padding: 11px 10px 11px 0;
      background: linear-gradient(90deg, transparent, var(--bs-light)); }
    .service_block_2 .icon_list .icon_list_icon {
      color: var(--bs-primary); }
    .service_block_2 .icon_list .icon_list_text {
      margin-top: 2px; }
  .service_block_2:hover .service_title a:after {
    opacity: 1;
    transform: translateX(0px); }

.pt-175 {
  padding-top: 175px; }

.pb-80 {
  padding-bottom: 80px; }

.service_facilities_group {
  margin: -10px; }
  .service_facilities_group > li {
    padding: 10px;
    flex: 0 0 50%; }
  .service_facilities_group .iconbox_block.layout_icon_left {
    display: flex;
    align-items: center;
    padding: 16px 20px 16px 16px; }
    .service_facilities_group .iconbox_block.layout_icon_left .iconbox_icon {
      width: 42px;
      height: 42px;
      margin: 0 16px 0 0;
      border-radius: 6px; }
      .service_facilities_group .iconbox_block.layout_icon_left .iconbox_icon img {
        max-width: 22px; }
    .service_facilities_group .iconbox_block.layout_icon_left .iconbox_title {
      font-size: 16px;
      line-height: 20px;
      color: var(--bs-dark); }
    .service_facilities_group .iconbox_block.layout_icon_left:hover .iconbox_title {
      color: var(--bs-primary); }

.service_pill_carousel {
  position: relative;
  pointer-events: none; }
  .service_pill_carousel:before, .service_pill_carousel:after {
    top: 0;
    bottom: 0;
    z-index: 2;
    content: '';
    width: 150px;
    position: absolute; }
  .service_pill_carousel:before {
    left: 0;
    background-image: linear-gradient(90deg, var(--bs-white), transparent); }
  .service_pill_carousel:after {
    right: 0;
    background-image: linear-gradient(90deg, transparent, var(--bs-white)); }
  .service_pill_carousel .swiper-wrapper {
    transition-timing-function: linear !important; }
    .service_pill_carousel .swiper-wrapper:hover {
      animation: none; }
  .service_pill_carousel .service_pill_block {
    display: flex; }

.service_pill_block {
  gap: 15px;
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
  border-radius: 30px;
  align-items: center;
  display: inline-flex;
  color: var(--bs-dark);
  padding: 6px 20px 6px 6px;
  font-family: 'Axiforma Medium';
  border: 1px solid var(--bs-border-color); }
  .service_pill_block i {
    width: 40px;
    height: 40px;
    flex: 0 0 auto;
    border-radius: 100%;
    align-items: center;
    display: inline-flex;
    color: var(--bs-white);
    justify-content: center;
    background-color: var(--bs-primary); }

/* 4.09 - Service - End
================================================== */
/* 4.10 - Team - Start
================================================== */
.team_block {
  padding: 40px;
  text-align: center;
  background-color: var(--bs-light);
  border-radius: var(--bs-border-radius-sm); }
  .team_block .image_wrap {
    height: 388px;
    display: flex;
    position: relative;
    margin-bottom: 30px;
    padding: 30px 20px 0;
    align-items: flex-end;
    justify-content: center;
    background-color: var(--bs-white);
    border-radius: var(--bs-border-radius-sm); }
    .team_block .image_wrap img {
      max-height: 100%;
      transition: var(--bs-transition); }
    .team_block .image_wrap i {
      top: 50%;
      left: 50%;
      z-index: 2;
      width: 120px;
      height: 120px;
      font-size: 30px;
      position: absolute;
      align-items: center;
      border-radius: 100%;
      display: inline-flex;
      color: var(--bs-white);
      justify-content: center;
      background-color: var(--bs-primary);
      transform: translate(-50%, -50%) scale(0);
      transition: transform var(--bs-transition); }
  .team_block .social_icons_block a {
    width: 34px;
    height: 34px; }
  .team_block:hover .image_wrap img {
    filter: blur(2px); }
  .team_block:hover .image_wrap i {
    transform: translate(-50%, -50%) scale(1); }
  .team_block .team_member_name {
    line-height: 1;
    font-size: 22px;
    margin-bottom: 14px; }
    .team_block .team_member_name a {
      color: var(--bs-dark); }
      .team_block .team_member_name a:hover {
        color: var(--bs-primary); }
  .team_block .team_member_designation {
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 2px;
    margin-bottom: 22px;
    color: var(--bs-dark);
    text-transform: uppercase;
    font-family: 'Axiforma Medium'; }

/* 4.10 - Team - End
================================================== */
/* 5.01 - About Page - Start
================================================== */
.about_image_1 {
  position: relative;
  margin-left: -273px; }
  .about_image_1 img:nth-child(2) {
    top: 170px;
    left: 200px;
    max-width: 75px;
    position: absolute; }
  .about_image_1 img:nth-child(3) {
    right: 40px;
    bottom: 105px;
    max-width: 80px;
    position: absolute; }

.about_and_case_section {
  padding: 180px 0 81px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center; }
  .about_and_case_section .shape_image_1 {
    top: 0;
    left: 0;
    right: 0;
    text-align: center; }
    .about_and_case_section .shape_image_1 img {
      width: calc(100% - 530px);
      transform: translateX(5px); }

.about_image_2 {
  gap: 20px;
  display: flex;
  position: relative;
  align-items: flex-start;
  margin-left: 50px; }
  .about_image_2 .image_wrap {
    flex: 0 0 auto;
    max-width: 198px;
    overflow: hidden;
    border-radius: var(--bs-border-radius); }
  .about_image_2 .space_line {
    top: 142px;
    left: 218px;
    max-width: 368px;
    position: absolute; }

.about_funfact_info {
  z-index: 1;
  width: 472px;
  flex: 0 0 auto;
  padding: 20px 30px;
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center; }
  .about_funfact_info .customer_count > ul {
    margin-bottom: 12px; }
    .about_funfact_info .customer_count > ul > li {
      width: 50px;
      height: 50px;
      font-size: 15px;
      overflow: hidden;
      line-height: 28px;
      border-radius: 100%;
      align-items: center;
      display: inline-flex;
      color: var(--bs-white);
      justify-content: center;
      border: 2px solid var(--bs-white);
      background-color: var(--bs-primary); }
      .about_funfact_info .customer_count > ul > li:not(:first-child) {
        margin-left: -15px; }
  .about_funfact_info .about_funfact_counter {
    gap: 50px;
    display: flex;
    margin-top: 46px;
    justify-content: flex-end; }
    .about_funfact_info .about_funfact_counter .counter_value {
      line-height: 1;
      font-size: 45px;
      font-weight: 700;
      display: inline-flex;
      color: var(--bs-dark);
      font-family: 'Axiforma Bold'; }
    .about_funfact_info .about_funfact_counter .funfact_title {
      color: #676767;
      font-size: 16px;
      font-weight: 400;
      font-family: 'Axiforma Regular'; }
  .about_funfact_info .btn {
    top: 0;
    right: -5px;
    position: absolute;
    border-radius: var(--bs-border-radius);
    padding: 0 20px; }
  .about_funfact_info .icon_globe {
    left: 22px;
    bottom: 22px;
    max-width: 40px;
    position: absolute; }
    .about_funfact_info .icon_globe:before, .about_funfact_info .icon_globe:after {
      top: 0;
      left: 0;
      width: 100%;
      content: '';
      height: 100%;
      position: absolute;
      border-radius: 100%;
      transition: all 0.33s ease;
      animation: ripple 1.5s linear infinite;
      border: 1px solid var(--bs-white); }
    .about_funfact_info .icon_globe:after {
      animation-delay: 0.9s; }

.about_image_3 {
  position: relative; }
  .about_image_3 .image_wrap {
    overflow: hidden;
    border-radius: var(--bs-border-radius); }
  .about_image_3 .funfact_block {
    left: -154px;
    bottom: 85px;
    position: absolute; }
    .about_image_3 .funfact_block .funfact_icon {
      background-color: #FFBE16; }

body:has(.page_banner_section + .intro_about_section) .page_banner_section {
  padding-bottom: 394px; }

.intro_about_section .intro_about_content {
  z-index: 1;
  position: relative;
  margin: -420px 0 60px; }
  .intro_about_section .intro_about_content .image_wrap {
    overflow: hidden;
    border-radius: var(--bs-border-radius-sm); }

/* 5.01 - About Page - End
================================================== */
/* 5.02 - Contact Page - Start
================================================== */
.contact_section:has(.decoration_item) {
  padding-top: 180px; }
.contact_section .shape_image_1 {
  left: 0;
  top: 50%;
  max-width: 192px;
  transform: translateY(-50%); }
.contact_section .shape_image_2 {
  right: 0;
  top: 50%;
  max-width: 298px;
  transform: translateY(-50%); }
.contact_section .shape_image_3 {
  top: 0;
  left: 0;
  right: 0;
  text-align: center; }
  .contact_section .shape_image_3 img {
    width: calc(100% - 530px);
    transform: translateX(-5px); }

.contact_info_box .iconbox_block {
  padding: 60px 30px 52px; }
.contact_info_box .iconbox_block .iconbox_icon img {
  max-width: 24px; }
.contact_info_box .iconbox_block .iconbox_icon {
  width: 60px;
  height: 60px;
  margin: 0 0 32px; }
.contact_info_box .iconbox_block .iconbox_title {
  line-height: 1;
  font-size: 18px; }

.contact_method_box {
  padding: 46px 50px 50px;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius);
  box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3); }
  .contact_method_box .contact_method_list {
    margin-bottom: 50px; }

.contact_method_list {
  gap: 10px; }
  .contact_method_list a {
    gap: 20px;
    align-items: center;
    display: inline-flex;
    color: var(--bs-dark); }
    .contact_method_list a:hover {
      color: var(--bs-primary); }
  .contact_method_list .icon {
    width: 40px;
    height: 40px;
    flex: 0 0 auto;
    align-items: center;
    border-radius: 100%;
    display: inline-flex;
    color: var(--bs-white);
    justify-content: center;
    background-color: var(--bs-primary); }
    .contact_method_list .icon i {
      animation: none; }
  .contact_method_list .text {
    font-size: 17px;
    font-weight: 500;
    font-family: 'Axiforma Medium'; }

.support_step {
  gap: 10px;
  z-index: 1;
  position: relative; }
  .support_step:before {
    top: 0;
    bottom: 0;
    left: 20px;
    width: 1px;
    content: '';
    z-index: -1;
    position: absolute;
    background-color: var(--bs-primary); }
  .support_step > li {
    gap: 10px;
    display: flex;
    align-items: center; }
  .support_step .serial_number {
    width: 40px;
    height: 40px;
    display: flex;
    flex: 0 0 auto;
    padding-top: 4px;
    font-weight: 500;
    border-radius: 100%;
    align-items: center;
    justify-content: center;
    color: var(--bs-primary);
    font-family: 'Axiforma Medium';
    background-color: var(--bs-white);
    border: 1px solid var(--bs-primary); }
  .support_step .text {
    font-size: 17px;
    padding-top: 4px;
    font-weight: 500;
    font-family: 'Axiforma Medium'; }

.instant_contact_form {
  padding: 50px;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius);
  box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3); }
  .instant_contact_form .small_title {
    line-height: 1;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 26px;
    color: var(--bs-dark);
    text-transform: uppercase;
    font-family: 'Axiforma Medium'; }
    .instant_contact_form .small_title i {
      color: var(--bs-primary); }
  .instant_contact_form .form_title {
    font-size: 26px;
    font-weight: 600;
    line-height: 36px;
    margin-bottom: 19px;
    font-family: 'Axiforma SemiBold'; }
  .instant_contact_form .form-group .form-control,
  .instant_contact_form .form-group .form-select {
    box-shadow: none;
    padding-left: 50px;
    caret-color: var(--bs-dark); }
    .instant_contact_form .form-group .form-control:focus,
    .instant_contact_form .form-group .form-select:focus {
      background-color: var(--bs-light);
      border-color: rgba(0, 68, 235, 0.1); }
  .instant_contact_form .form-group .input_title {
    top: 22px;
    left: 22px;
    position: absolute; }
    .instant_contact_form .form-group .input_title i {
      color: #676767;
      animation: none; }

.gmap_canvas iframe {
  width: 100%;
  height: 630px;
  border-radius: var(--bs-border-radius-sm); }

/* 5.02 - Contact Page - End
================================================== */
/* 5.03 - Details Pages - Start
================================================== */
[class*="_details_section"] .icon_list.unordered_list_block {
  gap: 6px; }
[class*="_details_section"] .icon_list > li {
  font-size: 18px;
  line-height: 32px; }
[class*="_details_section"] p {
  font-size: 18px;
  font-weight: 500;
  line-height: 32px;
  margin-bottom: 38px;
  font-family: 'Axiforma Medium'; }
[class*="_details_section"] hr {
  margin: 30px 0;
  background-color: #CCE3FF; }
[class*="_details_section"] .accordion p {
  font-size: 16px;
  line-height: 30px; }
[class*="_details_section"] .post_meta_wrap {
  row-gap: 15px;
  display: flex;
  column-gap: 50px;
  align-items: center; }
  [class*="_details_section"] .post_meta_wrap .post_meta {
    margin-top: 3px; }
[class*="_details_section"] .category_btns_group a {
  border: 1px solid #CCE3FF; }
  [class*="_details_section"] .category_btns_group a:hover {
    border-color: var(--bs-primary); }

.details_item_image {
  overflow: hidden;
  position: relative;
  margin-bottom: 50px;
  border-radius: var(--bs-border-radius-sm); }

.details_item_title {
  font-size: 40px;
  line-height: 55px;
  margin-bottom: 20px; }

.details_item_info_title {
  font-size: 35px;
  line-height: 50px;
  margin-bottom: 17px; }

.team_member_details_card {
  padding: 40px;
  display: flex;
  align-items: center;
  margin-bottom: 100px;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius-sm); }
  .team_member_details_card .team_member_image {
    height: 454px;
    flex: 0 0 490px;
    overflow: hidden;
    margin: 0 100px 0 0;
    display: inline-flex;
    padding: 10px 40px 0;
    align-items: flex-end;
    justify-content: center;
    background-color: var(--bs-light);
    border-radius: var(--bs-border-radius-sm); }
    .team_member_details_card .team_member_image img {
      height: 100%; }
  .team_member_details_card .team_member_name {
    font-size: 35px;
    line-height: 50px;
    margin-bottom: 30px; }
  .team_member_details_card .icon_list {
    gap: 14px;
    margin-bottom: 40px; }
    .team_member_details_card .icon_list > li {
      font-size: 18px;
      line-height: 30px; }
    .team_member_details_card .icon_list strong {
      color: var(--bs-dark); }
  .team_member_details_card .social_title {
    line-height: 1;
    font-size: 18px;
    margin-bottom: 15px; }

.portfolio_details_info_list {
  row-gap: 6px;
  column-gap: 50px; }

.content_layer_group > li {
  cursor: pointer;
  position: relative; }
  .content_layer_group > li:not(:first-child) {
    margin-top: -40px; }
  .content_layer_group > li[aria-expanded=true] {
    z-index: 1; }
    .content_layer_group > li[aria-expanded=true] span {
      position: relative;
      color: var(--bs-white);
      border-color: var(--bs-primary);
      background-color: var(--bs-primary); }
.content_layer_group span {
  height: 147px;
  display: flex;
  flex-wrap: wrap;
  font-size: 24px;
  font-weight: 500;
  max-width: 452px;
  line-height: 36px;
  border-radius: 100%;
  align-items: center;
  color: var(--bs-dark);
  justify-content: center;
  font-family: 'Axiforma Medium';
  border: 1px solid var(--bs-dark);
  transition: var(--bs-transition); }

.blog_details_section .details_item_info_title {
  font-size: 26px;
  line-height: 38px; }

.blog_details_audio {
  margin-bottom: 40px; }
  .blog_details_audio .audio_play_btn {
    gap: 26px;
    color: #676767;
    font-size: 15px;
    font-weight: 500;
    border-radius: 50px;
    align-items: center;
    display: inline-flex;
    padding: 5px 30px 5px 5px;
    border: 1px solid transparent;
    font-family: 'Axiforma Medium';
    background-color: var(--bs-white); }
    .blog_details_audio .audio_play_btn:hover {
      border-color: var(--bs-primary); }
    .blog_details_audio .audio_play_btn i {
      width: 35px;
      height: 35px;
      flex: 0 0 auto;
      padding-left: 4px;
      border-radius: 100%;
      align-items: center;
      display: inline-flex;
      color: var(--bs-white);
      justify-content: center;
      background-color: var(--bs-primary); }
    .blog_details_audio .audio_play_btn span {
      margin-bottom: -4px; }

.post_author_box {
  gap: 30px;
  padding: 40px;
  display: flex;
  margin: 80px 0;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius-sm); }
  .post_author_box .author_image {
    width: 150px;
    height: 150px;
    flex: 0 0 auto;
    overflow: hidden;
    border-radius: 100%; }
  .post_author_box .author_name {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 0;
    line-height: 26px;
    font-family: 'Axiforma SemiBold'; }
  .post_author_box .author_designation {
    font-size: 14px;
    font-weight: 500;
    line-height: 30px;
    margin-bottom: 12px;
    color: var(--bs-body-color);
    font-family: 'Axiforma Medium'; }
  .post_author_box p {
    font-size: 16px;
    line-height: 30px;
    margin-bottom: 20px; }
  .post_author_box .author_social_icons {
    gap: 20px; }
    .post_author_box .author_social_icons a {
      display: block;
      color: var(--bs-dark); }
      .post_author_box .author_social_icons a:hover {
        color: var(--bs-primary); }

.other_posts_nav {
  gap: 50px;
  display: flex;
  align-items: center;
  margin-bottom: 80px;
  justify-content: space-between; }
  .other_posts_nav a {
    position: relative;
    color: var(--bs-dark); }
    .other_posts_nav a:hover {
      color: var(--bs-primary); }
    .other_posts_nav a:nth-child(2) {
      font-size: 24px;
      color: var(--bs-dark); }
      .other_posts_nav a:nth-child(2):hover {
        color: var(--bs-primary); }
    .other_posts_nav a:first-child, .other_posts_nav a:last-child {
      gap: 28px;
      display: flex;
      border-radius: 20px;
      align-items: center;
      padding: 26px 50px 23px;
      background-color: var(--bs-white); }
      .other_posts_nav a:first-child i, .other_posts_nav a:last-child i {
        z-index: 1;
        font-size: 30px;
        position: relative; }
        .other_posts_nav a:first-child i:after, .other_posts_nav a:last-child i:after {
          top: 50%;
          z-index: -1;
          width: 30px;
          height: 30px;
          content: '';
          position: absolute;
          border-radius: 100%;
          transform: translateY(-50%);
          background-color: var(--bs-light); }
    .other_posts_nav a:first-child i:after {
      left: -8px; }
    .other_posts_nav a:first-child span {
      text-align: right; }
    .other_posts_nav a:last-child i:after {
      right: -8px; }
    .other_posts_nav a strong {
      display: block;
      font-size: 16px;
      margin-bottom: 2px; }
    .other_posts_nav a small {
      opacity: 0.8;
      display: block; }

.comment_area {
  margin: 80px 0; }

.comments_list {
  gap: 60px; }
  .comments_list > li .comments_list {
    gap: 30px;
    padding: 30px 0 0 110px; }

.comment_item {
  gap: 40px;
  display: flex; }
  .comment_item .comment_author_thumbnail {
    width: 80px;
    height: 80px;
    flex: 0 0 auto;
    overflow: hidden;
    border-radius: 100%; }
  .comment_item .comment_author_content {
    position: relative; }
  .comment_item .comment_author_name {
    line-height: 1;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    font-family: 'Axiforma SemiBold'; }
  .comment_item .comment_time {
    line-height: 1;
    font-size: 14px;
    margin-bottom: 20px;
    font-family: 'Axiforma Medium'; }
  .comment_item .comment_reply_btn {
    top: 0;
    right: 0;
    line-height: 1;
    font-size: 12px;
    font-weight: 700;
    position: absolute;
    border-radius: 50px;
    color: var(--bs-dark);
    padding: 10px 20px 9px;
    font-family: 'Axiforma Bold';
    background-color: var(--bs-white);
    box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3); }
    .comment_item .comment_reply_btn:hover {
      color: var(--bs-white);
      background-color: var(--bs-primary); }

/* 5.03 - Details Pages - End
================================================== */
/* 5.04 - Pricing Page - Start
================================================== */
.pricing_policy_wrap > div:not(:last-child) {
  border-style: solid;
  border-width: 0 1px 0 0;
  border-color: var(--bs-border-color); }
.pricing_policy_wrap > div .iconbox_block {
  margin: auto; }
.pricing_policy_wrap > div:first-child .iconbox_block {
  margin: auto auto auto 0; }
.pricing_policy_wrap > div:last-child .iconbox_block {
  margin: auto 0 auto auto; }
.pricing_policy_wrap .iconbox_block {
  padding: 0;
  transform: unset;
  max-width: 290px;
  box-shadow: none;
  border-radius: 0px; }
  .pricing_policy_wrap .iconbox_block .iconbox_icon {
    width: 60px;
    height: 60px; }
  .pricing_policy_wrap .iconbox_block .iconbox_title {
    font-size: 20px;
    line-height: 26px;
    margin-bottom: 12px; }

.pricing_toggle_btn {
  margin-bottom: 70px; }
  .pricing_toggle_btn button {
    gap: 4px;
    padding: 6px;
    font-size: 14px;
    font-weight: 700;
    align-items: center;
    border-radius: 65px;
    display: inline-flex;
    letter-spacing: 0.3px;
    justify-content: center;
    background-color: #CCE3FF;
    text-transform: uppercase;
    font-family: 'Axiforma Bold'; }
    .pricing_toggle_btn button small {
      font-size: 12px;
      font-weight: 500;
      border-radius: 29px;
      padding: 4px 7px 2px;
      font-family: 'Axiforma Medium';
      background-color: var(--bs-light); }
    .pricing_toggle_btn button span {
      padding: 14px 30px 12px; }
      .pricing_toggle_btn button span:last-child small {
        color: var(--bs-white);
        background-color: #FF5050; }
    .pricing_toggle_btn button:not(.active) span:last-child, .pricing_toggle_btn button.active span:first-child {
      border-radius: 32px;
      background-color: var(--bs-white);
      box-shadow: 0 4px 4px 0 rgba(116, 126, 148, 0.1); }

.pricing_block {
  padding: 40px;
  position: relative;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius-sm);
  box-shadow: 0 1px 2px 0 rgba(174, 191, 210, 0.3); }
  .pricing_block .best_plan {
    top: 0;
    right: 40px;
    max-width: 54px;
    position: absolute; }
  .pricing_block .table_head {
    gap: 30px;
    display: flex;
    align-items: center; }
  .pricing_block .pricing_price_value {
    width: 244px;
    flex: 0 0 auto;
    line-height: 1;
    font-size: 50px;
    font-weight: 700;
    text-align: center;
    border-radius: 20px;
    padding: 34px 15px 30px;
    font-family: 'Axiforma Bold'; }
    .pricing_block .pricing_price_value del {
      font-size: 20px;
      font-weight: 400;
      color: var(--bs-body-color);
      font-family: 'Axiforma Regular'; }
    .pricing_block .pricing_price_value small {
      font-size: 30px;
      font-weight: 600;
      font-family: 'Axiforma SemiBold'; }
    .pricing_block .pricing_price_value sub {
      display: block;
      line-height: 1;
      font-size: 14px;
      font-weight: 400;
      color: var(--bs-dark);
      font-family: 'Axiforma Regular'; }
      .pricing_block .pricing_price_value sub u {
        font-weight: 600;
        font-family: 'Axiforma SemiBold'; }
  .pricing_block .pricing_package_title {
    line-height: 1;
    font-size: 26px;
    margin-bottom: 10px; }
  .pricing_block .pricing_package_description {
    font-size: 15px;
    font-weight: 500;
    line-height: 23px;
    font-family: 'Axiforma Medium'; }
  .pricing_block .icon_list {
    columns: 2;
    display: block;
    column-gap: 20px;
    padding: 29px 0 25px; }
    .pricing_block .icon_list > li {
      padding: 5px 0; }
    .pricing_block .icon_list i {
      color: var(--bs-dark); }
    .pricing_block .icon_list .delete {
      opacity: .3; }
  .pricing_block .pricing_monthly {
    display: none; }
  .pricing_block.active .pricing_annually {
    display: none; }
  .pricing_block.active .pricing_monthly {
    display: block; }

/* 5.04 - Pricing Page - End
================================================== */
/* 5.05 - Home Pages - Start
================================================== */
.process_technology_review_section {
  padding: 176px 0 80px;
  overflow: hidden; }
  .process_technology_review_section .shape_image_1 {
    top: 0;
    left: 0;
    max-width: 196px; }
  .process_technology_review_section .shape_image_2 {
    top: 42%;
    right: -12px;
    max-width: 200px; }
  .process_technology_review_section .shape_image_3 {
    bottom: 0;
    left: -8px;
    max-width: 142px; }
  .process_technology_review_section .shape_image_4 {
    top: 0;
    left: 0;
    right: 0;
    text-align: center; }
    .process_technology_review_section .shape_image_4 img {
      width: calc(100% - 530px); }

.deals_winner_customers {
  padding: 50px;
  background-color: var(--bs-white);
  border-radius: var(--bs-border-radius);
  box-shadow: 0 4px 23px 0 rgba(174, 191, 210, 0.3); }
  .deals_winner_customers .title_text {
    font-size: 45px;
    font-weight: 700;
    margin-bottom: 91px;
    font-family: 'Axiforma Bold'; }
    .deals_winner_customers .title_text mark {
      font-size: 55px; }
  .deals_winner_customers .rating_block {
    margin: 20px 0 10px; }
  .deals_winner_customers .review_short_info .review_counter {
    font-size: 14px; }

.web_development_technologies .iconbox_block {
  transform: unset; }
  .web_development_technologies .iconbox_block .iconbox_icon {
    width: 100px;
    height: 100px;
    margin: 0 0 16px;
    background-size: cover;
    background-repeat: no-repeat;
    background-color: transparent;
    background-position: center top -3px;
    background-image: url("../images/shapes/shape_polygon.webp"); }
  .web_development_technologies .iconbox_block .iconbox_title {
    line-height: 1;
    font-size: 20px;
    font-weight: 400;
    font-family: "Axiforma Regular"; }

body.index_business_consulting {
  padding: 30px;
  --bs-light: #F1F6FC;
  --bs-light-rgb: 241, 246, 252; }
  body.index_business_consulting main > div,
  body.index_business_consulting main > section {
    margin-bottom: 30px; }
  body.index_business_consulting .backtotop {
    right: 45px;
    bottom: 64px; }

.empowering_services {
  gap: 50px; }
  .empowering_services > li {
    position: relative; }
    .empowering_services > li:not(:last-child):before {
      left: 46px;
      top: 109px;
      width: 2px;
      content: "";
      opacity: 0.2;
      height: 50px;
      position: absolute;
      border-left: 1px dashed #0D121E; }
  .empowering_services .iconbox_block.layout_icon_left {
    padding: 0;
    transform: unset;
    border-radius: 0;
    box-shadow: none;
    background-color: transparent; }
  .empowering_services .iconbox_block.layout_icon_left .iconbox_icon {
    width: 96px;
    height: 96px;
    border-radius: 100%;
    background-color: var(--bs-white);
    box-shadow: 0 40px 34px 0 rgba(29, 40, 78, 0.06); }
  .empowering_services .iconbox_block.layout_icon_left .iconbox_icon img {
    max-width: 40px; }
  .empowering_services .iconbox_block.layout_icon_left .iconbox_title {
    font-size: 24px;
    line-height: 32px;
    margin-bottom: 8px; }
  .empowering_services .iconbox_block p {
    font-size: 18px; }

.business_consulting_service_section {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center; }
  .business_consulting_service_section .container {
    max-width: 1630px; }

.row.business_consulting_services {
  margin: -7px; }
  .row.business_consulting_services > [class*="col-"] {
    padding: 7px; }
  .row.business_consulting_services .iconbox_block {
    padding: 80px 50px 72px; }
    .row.business_consulting_services .iconbox_block .iconbox_title a {
      color: var(--vs-dark); }
      .row.business_consulting_services .iconbox_block .iconbox_title a:hover {
        color: var(--bs-primary); }
  .row.business_consulting_services .iconbox_block .iconbox_icon {
    width: 96px;
    height: 96px;
    margin: 0 0 47px;
    border: 1px solid #F5F5F5;
    background-color: var(--bs-white);
    box-shadow: 0 34px 40px 0 rgba(29, 40, 78, 0.06); }

.review_and_about_section .container {
  max-width: 1630px; }

.review_bg_box {
  background-size: 930px;
  padding: 112px 100px 120px;
  background-repeat: no-repeat;
  background-position: center center;
  border-radius: var(--bs-border-radius); }

/* 5.05 - Home Pages - End
================================================== */
/* 
Responsive For Mobile & Tablet Devices
================================================== 
* Project Name   :  Techco – IT Solutions & Technology, Business Consulting, Software Company Site Template
* File           :  CSS Base
* Version        :  1.0.0
* Author         :  XpressBuddy (https://themeforest.net/user/xpressbuddy/portfolio)
* Developer			 :	webrok (https://www.fiverr.com/webrok?up_rollout=true)
*	CSS code for responsive layout To make responsive
================================================== 
*/
/* Media Screen 1440px - Start
================================================== */
@media screen and (max-width: 1560px) {
  .main_menu_list {
    gap: 20px; }

  .mega_menu_wrapper {
    padding: 30px 0; }

  .mega_menu_wrapper .megamenu_pages_wrapper {
    margin: 0; }

  .iconbox_block_2 .iconbox_title {
    font-size: 16px;
    margin: 10px 0 0; }

  .mega_menu_wrapper .site_author {
    margin: 0;
    padding: 30px; }

  .mega_menu_wrapper .site_author p {
    font-size: 15px;
    margin: 18px 0 0;
    line-height: 24px; }

  .mega_menu_wrapper .row:has(> [class*=col-] > .megamenu_widget) {
    margin: 0px -20px; }

  .mega_menu_wrapper .row:has(> [class*=col-] > .megamenu_widget) > [class*=col-] {
    padding: 30px 20px; }

  .mega_menu_wrapper .megamenu_widget ul li {
    font-size: 16px; }

  .mega_menu_wrapper .megamenu_widget ul {
    gap: 16px; }

  .mega_menu_wrapper .social_area {
    padding: 20px 0; }

  .megamenu_case {
    padding: 30px; }

  .megamenu_case h4 {
    font-size: 22px;
    margin: 6px 0 0; }

  .megamenu_case h3 {
    font-size: 12px; }

  .megamenu_case img {
    margin: 24px 0;
    display: block;
    max-width: 220px; }

  .iconbox_block_2 .description {
    font-size: 14px; }

  .it_solution_hero_content {
    padding: 111px 80px 113px; }

  .avatar_group > li {
    width: 40px;
    height: 40px; }

  .it_solution_hero_images .worldwide_clients {
    padding: 50px 60px; }

  .it_solution_hero_images .categories {
    padding: 115px 20px; }

  body.index_business_consulting {
    padding: 0px; }

  .site_header_3 .main_menu_list {
    gap: 30px; }

  .site_header_3 {
    padding: 30px 0; }

  .site_header_3.sticky {
    padding: 10px 0; }

  .site_header_3 .mega_menu_wrapper {
    top: 58px; }

  body.index_business_consulting .container:not(.site_header.site_header_3 .container),
  .business_consulting_hero_section .container {
    max-width: 1140px; }

  .business_consulting_hero_section h1 {
    font-size: 66px;
    line-height: 70px; }

  .business_consulting_hero_section p {
    font-size: 20px;
    line-height: 30px;
    margin: 14px 0 34px 0; }

  .business_consulting_hero_section .shape_1 {
    max-width: 530px; }

  body.index_business_consulting main > div,
  body.index_business_consulting main > section {
    margin-bottom: 0px; }
  body.index_business_consulting .backtotop {
    right: 15px;
    bottom: 22px; }

  .empowering_services .iconbox_block.layout_icon_left .iconbox_title {
    line-height: 1;
    font-size: 20px; }

  .empowering_services .iconbox_block p {
    font-size: 16px; }

  .row.business_consulting_services .iconbox_block {
    padding: 36px; }
  .row.business_consulting_services .iconbox_block .iconbox_title {
    font-size: 24px;
    line-height: 32px;
    margin-bottom: 16px; }

  .row.business_consulting_services .iconbox_block .iconbox_icon {
    margin: 0 0 30px; }

  .review_and_about_section .container {
    max-width: 1630px; }

  .container:has(.review_bg_box) {
    max-width: 100% !important; }

  .review_bg_box {
    padding: 112px 60px 120px; }

  .heading_block .heading_text {
    font-size: 40px;
    line-height: 50px; }

  .blog_post_block_2 .blog_post_content {
    padding: 40px 30px; }

  .software_company_hero_section .shape_image_2,
  .software_company_hero_section .shape_image_1 {
    left: 15px;
    max-width: 70%; }

  .site_header_3 .dropdown-menu.mega_menu_wrapper {
    top: 77px; } }
@media screen and (max-width: 1440px) {
  .it_solution_hero_images .worldwide_clients .counter_value {
    font-size: 36px;
    margin: 0 0 10px; }

  .it_solution_hero_images .worldwide_clients p {
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 55px; }

  .it_solution_hero_images .categories a {
    font-size: 14px; }

  .it_solution_hero_images .categories {
    border-radius: 0px 100px 0 20px; }

  .it_solution_hero_images .worldwide_clients {
    border-radius: 0px 20px 0px 100px; }

  .it_solution_hero_content {
    padding: 99px 80px 101px; }

  .site_header_2 .main_menu_list > li > a {
    padding: 6px 12px; }

  .software_company_hero_section .hero_image {
    bottom: 72px; }

  .about_image_2 {
    flex-direction: column; }

  .about_image_2 .space_line {
    left: 0;
    top: auto;
    bottom: -58px; }

  .site_header_2.sticky .main_menu_list .dropdown-menu:before {
    top: -22px;
    height: 22px; }

  .site_header_2.sticky .main_menu_list .dropdown-menu {
    margin: 20px 0 0; }

  .site_header_2 .dropdown-menu.mega_menu_wrapper {
    top: 82px; }

  .site_header_2.sticky .dropdown-menu.mega_menu_wrapper {
    top: 73px; }

  .engine_image {
    float: none;
    margin: auto;
    width: 614px;
    height: 614px; }

  .engine_image .image_wrap_3 {
    max-width: 490px; }

  .engine_image .image_wrap_2 {
    max-width: 410px; }

  .engine_image .image_wrap_1 {
    max-width: 120px; }

  .feature_partners_section::before {
    width: 15%; } }
@media screen and (max-width: 1360px) {
  .it_solution_hero_images .worldwide_clients p {
    margin-bottom: 16px; }

  .it_solution_hero_images .categories {
    padding: 106px 20px; }

  .software_company_hero_section .shape_image_4,
  .software_company_hero_section .shape_image_3 {
    max-width: 270px; } }
/* Media Screen 1440px - End
================================================== */
/* Media Screen 1199px - Start
================================================== */
@media screen and (max-width: 1199px) {
  .main_menu_list > li > a {
    font-size: 14px; }

  .main_menu_list {
    gap: 14px; }

  .iconbox_block_2 .icon_title_wrap {
    flex-direction: column; }

  .author_box {
    gap: 14px;
    flex-direction: column; }

  .author_box .author_image {
    align-self: flex-start; }

  .team_block .image_wrap {
    height: 290px; }

  .team_member_details_card .team_member_image {
    margin: 0 50px 0 0; }

  .funfact_block:has(.bottom_line) {
    padding: 65px 36px 50px; }

  .funfact_block:has(.bottom_line) .counter_value {
    font-size: 50px;
    margin-bottom: 16px; }

  .team_member_details_card {
    gap: 30px;
    margin-bottom: 80px;
    flex-direction: column;
    align-items: flex-start;
    padding: 30px 30px 40px; }

  .team_member_details_card .team_member_image {
    margin: 0;
    flex: 0 0 100%; }

  .team_member_details_card .team_member_name {
    line-height: 1;
    font-size: 34px;
    margin: 8px 0 24px; }

  .it_solution_hero_section .row [class*="col"] {
    width: 100%; }

  .it_solution_hero_images {
    align-items: center; }

  .it_solution_hero_images .worldwide_clients {
    padding: 100px 60px; }

  .site_header_2 .main_menu_list {
    margin: 0 -40px; }

  .software_company_hero_section .hero_image {
    bottom: 52px; }

  .software_company_hero_section {
    padding: 170px 0 130px; }

  .software_company_hero_section h1 {
    font-size: 48px;
    line-height: 60px; }

  .service_block_2 .service_title {
    font-size: 20px;
    line-height: 22px; }

  .case_study_block .case_study_content {
    padding: 40px; }

  .contact_method_box,
  .instant_contact_form {
    padding: 40px; }

  .review_bg_box {
    padding: 112px 30px 120px; }

  .review_block_3 .review_commtent {
    font-size: 15px;
    line-height: 21px;
    margin: 18px 0 25px; }

  .review_block_3 .review_admin {
    gap: 10px;
    flex-direction: column;
    align-items: flex-start; }

  .funfact_block.capsule_layout .counter_value {
    font-size: 26px; }

  .blog_post_block_2 .blog_post_content {
    max-width: 90%;
    padding: 40px 30px;
    margin: -80px auto auto; }

  .blog_post_block_2 {
    flex-direction: column-reverse; }

  body.index_business_consulting .backtotop {
    bottom: 55px; } }
/* Media Screen 1199px - End
================================================== */
/* Media Screen 1024px - Start
================================================== */
@media screen and (max-width: 1024px) {
  .blog_post_block.image_left_layout {
    align-items: flex-start;
    flex-direction: column; }

  .blog_post_block.content_over_layout .blog_post_title {
    font-size: 30px;
    line-height: 40px; }

  .blog_post_block.image_left_layout .blog_post_image {
    width: 100%; }
    .blog_post_block.image_left_layout .blog_post_image img {
      width: 100%; }

  .pricing_block .table_head {
    gap: 20px;
    flex-direction: column;
    align-items: flex-start; }

  .pricing_block .pricing_price_value {
    order: 1; }

  .pricing_block .icon_list {
    columns: 1; }

  .service_block .service_content {
    padding: 30px; }

  .service_block .service_title {
    font-size: 24px;
    line-height: 30px;
    margin-bottom: 24px; }

  .service_block .links_wrapper {
    gap: 24px;
    align-items: flex-start;
    flex-direction: column-reverse; }

  .portfolio_carousel .swiper-slide {
    padding: 0 15px; }

  .software_company_hero_section .hero_image {
    display: none; }

  .software_company_hero_section [class*="col"] {
    width: 100%; }

  .site_header_2 .main_menu_list {
    margin: 0 -60px; }

  .service_section .shape_image_4,
  .service_section .shape_image_5,
  .faq_section .shape_image_2,
  .blog_section .shape_image_2 {
    max-width: 130px; }

  .about_and_case_section .shape_image_1 img,
  .process_technology_review_section .shape_image_4 img,
  .contact_section .shape_image_3 img,
  .footer_layout_2 .shape_image_1 img {
    width: calc(100% - 265px); }

  .service_section .shape_image_3,
  .faq_section .shape_image_1 img {
    width: calc(100% - 130px); }

  .process_technology_review_section .shape_image_1,
  .service_section .shape_image_1,
  .process_technology_review_section .shape_image_3,
  .contact_section .shape_image_1,
  .contact_section .shape_image_2 {
    max-width: 80px; }

  .process_technology_review_section .shape_image_2 {
    max-width: 100px; }

  .deals_winner_customers {
    padding: 30px; }

  .deals_winner_customers .title_text {
    font-size: 30px; }

  .deals_winner_customers .title_text mark {
    font-size: 36px; }

  .review_block_2 .review_title {
    font-size: 24px;
    line-height: 30px;
    margin-bottom: 20px; }

  .review_block_2 .review_commtent {
    font-size: 18px;
    margin-bottom: 36px; }

  .service_section .shape_image_2 {
    max-width: 130px; }

  .about_image_2 {
    margin-bottom: 80px; }

  .site_header_3 .main_menu_list {
    gap: 20px;
    padding: 0 30px; }

  .funfact_block.capsule_layout .funfact_icon {
    width: 60px;
    height: 60px;
    background-color: #47B16A; }

  .funfact_block.capsule_layout .funfact_content {
    gap: 4px;
    padding: 0 25px; }

  .business_consulting_hero_image .funfact_block.capsule_layout:nth-child(4) {
    right: -15px; }

  .business_consulting_hero_image {
    padding: 0; }

  .software_company_hero_section [class*=shape_image_] {
    display: none; }

  .software_company_hero_section .content_wrap {
    text-align: center; }

  .software_company_hero_section p {
    margin: 0 auto 37px; }

  .software_company_hero_section .step_list {
    text-align: left;
    display: inline-flex; }

  .software_company_hero_section .btns_group {
    justify-content: center !important; }

  .blog_section .shape_image_1 {
    max-width: 80px; } }
/* Media Screen 1024px - End
================================================== */
/* Media Screen 991px - Start
================================================== */
@media screen and (max-width: 991px) {
  .site_header .header_btns_group > li:first-child {
    display: inline-block; }

  .main_menu_list .dropdown-menu {
    margin: 0;
    min-width: 240px; }

  .mega_menu_wrapper {
    padding: 15px 0;
    border-radius: 10px;
    border: 1px solid var(--bs-border-color); }

  .main_menu_list {
    gap: 1px; }

  .site_header_1 .header_bottom {
    border-radius: 10px 10px 0 0; }

  .container,
  .site_header_1 .container,
  .site_header_2 .container,
  body.index_business_consulting .container,
  body.index_business_consulting .container:not(.site_header.site_header_3 .container),
  .business_consulting_hero_section .container {
    max-width: 730px; }

  .intro_about_section .intro_about_content {
    margin: -420px 0 30px; }

  .dropdown-menu.mega_menu_wrapper {
    max-height: 300px;
    overflow-y: scroll; }
    .dropdown-menu.mega_menu_wrapper::-webkit-scrollbar {
      width: 5px; }
    .dropdown-menu.mega_menu_wrapper::-webkit-scrollbar-thumb {
      background: #B7BAC2;
      border-radius: 4px; }

  .mega_menu_wrapper .social_area {
    gap: 10px;
    flex-direction: column;
    align-items: flex-start; }

  .iconbox_block_2 .icon_title_wrap {
    flex-direction: unset; }

  .iconbox_block_2 {
    background-color: var(--bs-light); }

  .megamenu_pages_wrapper > .row {
    margin: -2px; }
    .megamenu_pages_wrapper > .row > [class*="col-"] {
      padding: 2px; }

  .review_short_info_2 {
    padding: 15px;
    flex-direction: column;
    align-items: flex-start; }

  .team_block .image_wrap {
    height: 310px; }

  .footer_layout_1 .diract_contact_links {
    gap: 50px;
    padding: 50px 0;
    flex-wrap: wrap;
    justify-content: flex-start; }

  .site_footer .icon_list > li {
    font-size: 16px;
    line-height: 24px; }

  .footer_info_title {
    margin-bottom: 24px; }

  .site_footer .footer_main_content > .row {
    }
    .site_footer .footer_main_content > .row > [class*="col-"] {
      padding: 30px; }

  .footer_layout_1 .content_box {
    border-radius: 0 0 10px 10px; }

  .footer_layout_1 .footer_main_content {
    padding: 70px 0; }

  .blog_post_block.content_over_layout .image_wrap {
    height: 500px;
    display: flex; }
    .blog_post_block.content_over_layout .image_wrap img {
      object-fit: cover; }

  .sidebar {
    padding-top: 80px; }

  .details_item_title {
    font-size: 36px;
    line-height: 48px; }

  .pricing_policy_wrap > div:not(:last-child) {
    border-width: 0 0 1px 0; }

  .pricing_policy_wrap > div .iconbox_block {
    margin: 0;
    padding: 30px 0;
    max-width: 100%; }

  .pricing_toggle_btn button {
    border-radius: 28px;
    flex-direction: column; }

  .pricing_toggle_btn button span {
    width: 100%; }

  .pricing_toggle_btn {
    margin-bottom: 30px; }

  .about_image_1 {
    margin: 0; }

  .site_header_2 .main_menu_list {
    margin: auto; }

  .site_header_2:not(.sticky) .main_menu_list > li > a {
    color: var(--bs-dark); }

  .site_header_2 .mobile_menu_btn {
    color: var(--bs-white);
    border: 1px solid var(--bs-white); }
    .site_header_2 .mobile_menu_btn:hover {
      border-color: var(--bs-primary); }

  .site_header_2.sticky .mobile_menu_btn {
    color: var(--bs-dark);
    border: 1px solid var(--bs-light); }
    .site_header_2.sticky .mobile_menu_btn:hover {
      color: var(--bs-white);
      border-color: var(--bs-primary); }

  .site_header_2 .main_menu {
    top: 95px; }

  .site_header_2.sticky .main_menu {
    top: 74px; }

  .case_study_block {
    flex-direction: column-reverse; }

  .case_study_block .case_study_image {
    max-width: 100%; }

  .content_layer_group span {
    margin: auto;
    height: 100px;
    font-size: 20px;
    max-width: 320px;
    line-height: 28px; }

  .content_layer_group > li:not(:first-child) {
    margin-top: -20px; }

  .site_header_2:not(.sticky) .main_menu_list > li:hover > a,
  .site_header_2:not(.sticky) .main_menu_list > li.active > a,
  .site_header_2 .main_menu_list > li:hover > a,
  .site_header_2 .main_menu_list > li.active > a {
    border-color: var(--bs-light); }

  .site_header .main_menu_list .dropdown-menu:before {
    display: none; }

  .site_header_2 .main_menu_list .dropdown-menu {
    margin: 1px 0 0; }

  .site_header_3 .main_menu_list {
    gap: 0;
    padding: 15px;
    border-radius: 10px; }

  .site_header_3 .main_menu_list .dropdown-menu {
    margin: 0; }

  .site_header_3 .main_menu {
    top: 81px; }

  .site_header_3.sticky .main_menu {
    top: 71px; }

  .business_consulting_hero_section .shape_1 {
    display: none; }

  .about_image_3 .funfact_block {
    left: -100px; }

  .footer_layout_3 .social_wrap {
    justify-content: flex-start; }

  .site_header.site_header_3.sticky {
    background-color: var(--bs-white);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08); }

  .about_image_1 img:nth-child(2) {
    top: 70px;
    left: 145px;
    max-width: 60px; }

  .about_image_1 img:nth-child(3) {
    right: 40px;
    bottom: 50px;
    max-width: 50px; } }
/* Media Screen 991px - End
================================================== */
/* Media Screen 767px - Start
================================================== */
@media screen and (max-width: 767px) {
  .site_header .header_btns_group > li:last-child {
    display: none; }

  .site_header .site_logo .badge {
    display: none; }

  .team_block {
    padding: 14px; }

  .team_block .image_wrap {
    margin: 0;
    border-radius: 14px; }

  .team_block .team_member_info {
    padding: 24px 15px; }

  .footer_layout_1 .footer_bottom {
    text-align: center; }

  body:has(.footer_layout_1) .backtotop {
    bottom: 100px; }

  .other_posts_nav {
    gap: 30px;
    flex-direction: column; }

  .footer_layout_3 .footer_bottom {
    text-align: center; }

  body.index_business_consulting .backtotop {
    bottom: 80px; }

  .footer_layout_3 .social_wrap {
    display: block; }

  .about_image_3 .funfact_block,
  .business_consulting_hero_image .funfact_block.capsule_layout:nth-child(2),
  .business_consulting_hero_image .funfact_block.capsule_layout:nth-child(3) {
    left: -15px; }

  .empowering_services > li:not(:last-child):before {
    top: 98px; }

  .review_block_2 .review_admin {
    margin-bottom: 30px; }

  .review_onecol_wrapper {
    padding: 37px 30px 30px; } }
/* Media Screen 767px - End
================================================== */
/* Media Screen 680px - Start
================================================== */
@media screen and (max-width: 680px) {
  .it_solution_hero_content {
    padding: 80px 40px; }

  .software_company_hero_section [class*=col] {
    width: 100%; }

  .about_image_1 img:nth-child(2) {
    top: 50px;
    left: 125px;
    max-width: 50px; }

  .about_image_1 img:nth-child(3) {
    right: 45px;
    bottom: 30px;
    max-width: 40px; }

  .engine_image {
    width: 500px;
    height: 500px;
    margin: 30px auto auto; }

  .engine_image .image_wrap_3 {
    max-width: 410px; }

  .engine_image .image_wrap_2 {
    max-width: 350px; }

  .engine_image .image_wrap_1 {
    max-width: 100px; } }
/* Media Screen 680px - End
================================================== */
/* Media Screen 575px - Start
================================================== */
@media screen and (max-width: 575px) {
  .heading_block .heading_text,
  .calltoaction_section .heading_block .heading_text {
    font-size: 36px;
    line-height: 46px; }

  .heading_block .heading_description {
    font-size: 16px;
    line-height: 26px; }

  .page_title {
    font-size: 48px;
    line-height: 58px; }

  .iconbox_block .iconbox_title {
    font-size: 26px;
    line-height: 30px; }

  .funfact_block .funfact_icon {
    height: 60px;
    margin-bottom: 40px; }

  .funfact_block .funfact_icon img {
    max-height: 60px; }

  .funfact_block .counter_value {
    font-size: 36px;
    margin-bottom: 2px; }

  .service_facilities_group {
    margin: -4px; }

  .service_facilities_group > li {
    padding: 4px;
    flex: 0 0 100%; }

  .diract_contact_links .iconbox_block.layout_icon_left p {
    font-size: 16px; }

  .blog_post_block.content_over_layout .blog_post_content {
    padding: 40px 40px 80px; }

  .blog_post_block.content_over_layout .blog_post_title {
    font-size: 26px;
    line-height: 36px; }

  .details_item_title {
    font-size: 30px;
    line-height: 42px; }

  .blog_details_section .details_item_info_title {
    font-size: 22px;
    line-height: 30px; }

  [class*=_details_section] p {
    font-size: 16px;
    line-height: 26px;
    margin-bottom: 30px; }

  [class*=_details_section] .icon_list > li {
    font-size: 16px;
    line-height: 26px; }

  .post_author_box .author_image {
    width: 100px;
    height: 100px; }

  .post_author_box {
    flex-direction: column; }

  .details_item_info_title {
    font-size: 26px;
    line-height: 32px; }

  .content_layer_group span {
    margin: auto;
    height: 120px;
    font-size: 18px;
    max-width: 300px;
    line-height: 28px; }

  .content_layer_group > li:not(:first-child) {
    margin-top: -30px; }

  .portfolio_block .portfolio_title {
    font-size: 20px;
    line-height: 30px; }

  .portfolio_block.portfolio_layout_2 .portfolio_content {
    padding: 30px 0px 20px 0px; }

  .page_title {
    font-size: 42px;
    line-height: 52px; }

  .team_member_details_card .team_member_name {
    font-size: 30px; }

  .it_solution_hero_images {
    gap: 15px; }

  .it_solution_hero_images > li {
    width: 100%;
    overflow: hidden;
    border-radius: 30px; }

  .it_solution_hero_images .worldwide_clients,
  .it_solution_hero_images .categories {
    padding: 40px;
    border-radius: 0; }

  .it_solution_hero_content h1 {
    font-size: 42px;
    line-height: 52px;
    margin: 12px 0 16px; }

  .it_solution_hero_section {
    padding: 30px 0; }

  .service_section .shape_image_4,
  .service_section .shape_image_5,
  .faq_section .shape_image_2,
  .blog_section .shape_image_2 {
    max-width: 60px; }

  .service_section .shape_image_3,
  .faq_section .shape_image_1 img {
    width: calc(100% - 60px); }

  .service_section .shape_image_3,
  .process_technology_review_section .shape_image_4,
  .faq_section .shape_image_1,
  .about_and_case_section .shape_image_1,
  .contact_section .shape_image_3,
  .footer_layout_2 .shape_image_1 {
    top: -5px; }

  .about_and_case_section .shape_image_1 img,
  .process_technology_review_section .shape_image_4 img,
  .contact_section .shape_image_3 img,
  .footer_layout_2 .shape_image_1 img {
    width: calc(100% - 120px); }

  .faq_accordion .text_a {
    position: static; }

  .faq_accordion .accordion-body {
    padding: 30px; }

  .blog_section .shape_image_1 {
    max-width: 80px; }

  .footer_layout_2 .footer_bottom {
    padding: 13px 0 10px;
    background-size: cover; }

  .feature_partners_section .title_text {
    padding: 0;
    max-width: 100%;
    position: static;
    margin-bottom: 30px; }

  .business_consulting_hero_section h1 {
    font-size: 54px;
    line-height: 62px; }

  .blog_post_block_2 .post_title {
    font-size: 24px;
    line-height: 32px; }

  .about_image_1 img:nth-child(2) {
    left: 90px; }

  .about_image_1 img:nth-child(3) {
    right: 30px; }

  .engine_image {
    width: 300px;
    height: 300px; }

  .engine_image .image_wrap_3 {
    max-width: 240px; }

  .engine_image .image_wrap_2 {
    max-width: 220px; }

  .engine_image .image_wrap_1 {
    max-width: 60px; } }
@media screen and (max-width: 533px) {
  .site_header .main_menu {
    top: 90px; }

  .site_header_1 .main_menu {
    top: 155px; }

  .site_header_1 + main {
    padding-top: 155px; } }
/* Media Screen 575px - End
================================================== */
/* Media Screen 425px - Start
================================================== */
@media screen and (max-width: 425px) {
  .review_short_info_2 {
    gap: 20px;
    width: 100%;
    padding: 18px 30px;
    flex-direction: column;
    align-items: flex-start; }

  .review_short_info_2 .review_admin_logo {
    width: auto;
    height: auto;
    border-width: 0 0 1px;
    justify-content: flex-start; }

  .review_short_info_2 .review_info_content {
    padding: 0;
    width: auto; }

  .btns_group:has(.review_short_info_2) {
    gap: 10px;
    flex-direction: column;
    align-items: flex-start; }

  .intro_about_section .intro_about_content {
    margin: -280px 0 30px; }

  body:has(.page_banner_section + .intro_about_section) .page_banner_section {
    padding-bottom: 200px; }

  .iconbox_block {
    padding: 40px 40px 35px; }

  .funfact_block {
    padding: 40px 30px;
    text-align: center; }

  .our_world_employees .title_text {
    font-size: 26px;
    line-height: 32px; }

  .blog_post_block.content_over_layout .image_wrap {
    height: 700px; }

  .blog_post_block.image_left_layout .blog_post_title {
    font-size: 24px;
    line-height: 32px; }

  .blog_post_block.image_left_layout .blog_post_content {
    padding: 30px; }

  .details_item_title {
    font-size: 26px;
    line-height: 36px; }

  .comment_item {
    gap: 20px;
    flex-direction: column; }

  .comments_list > li .comments_list {
    padding: 30px 0 0 50px; }

  .post_category_list a {
    font-size: 16px; }

  .gmap_canvas iframe {
    height: 350px; }

  .details_item_info_title {
    font-size: 22px;
    line-height: 28px; }

  .heading_block .heading_text,
  .calltoaction_section .heading_block .heading_text {
    font-size: 30px;
    line-height: 36px; }

  .service_block .service_image {
    height: 370px;
    display: flex; }
    .service_block .service_image img {
      object-fit: cover;
      width: 100%; }

  .it_solution_hero_content {
    padding: 60px 30px; }

  .it_solution_hero_content h1 {
    font-size: 32px;
    line-height: 42px; }

  .it_solution_hero_content p {
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 26px; }

  .portfolio_block .btn {
    position: static;
    margin-top: 30px; }

  .iconbox_block.layout_icon_left {
    gap: 20px;
    flex-direction: column; }

  .iconbox_block.layout_icon_left .iconbox_icon {
    margin: 0; }

  .software_company_hero_section h1 {
    font-size: 42px;
    line-height: 48px; }

  .service_section .shape_image_3, .process_technology_review_section .shape_image_4, .faq_section .shape_image_1, .about_and_case_section .shape_image_1, .contact_section .shape_image_3,
  .footer_layout_2 .shape_image_1 {
    top: -7px; }

  .service_block_2 {
    padding: 30px 20px; }

  .about_funfact_info .btn {
    position: static;
    margin-top: 22px; }

  .about_image_2 .space_line {
    display: none; }

  .about_image_2 {
    margin-bottom: 30px; }

  .case_study_block .case_study_content {
    padding: 30px 20px; }

  .case_study_block .case_technologies:before {
    margin-right: 20px; }

  .case_study_block .case_technologies {
    gap: 10px; }

  .faq_accordion .accordion-button {
    font-size: 18px;
    line-height: 26px; }

  .business_consulting_hero_section {
    padding: 140px 0 80px; }

  .business_consulting_hero_section h1 {
    font-size: 48px;
    line-height: 56px; }

  .creative_btn .btn_label {
    padding: 14px 34px 13px; }

  .creative_btn .btn_icon {
    width: 54px;
    height: 54px; }

  .business_consulting_hero_image .funfact_block.capsule_layout {
    position: static; }

  .business_consulting_hero_image {
    gap: 15px;
    display: flex;
    flex-direction: column; }

  .business_consulting_hero_image .funfact_block.capsule_layout:nth-child(4) {
    transform: unset;
    justify-content: center; }

  .empowering_services > li:not(:last-child):before {
    display: none; }

  .review_bg_box {
    padding: 80px 20px 88px; }

  .about_image_1 img:nth-child(2) {
    top: 20px;
    left: 70px;
    max-width: 30px; }

  .about_image_1 img:nth-child(3) {
    right: 28px;
    bottom: 20px;
    max-width: 30px; }

  .about_funfact_info .icon_globe {
    display: none; }

  .feature_partners_section::before {
    display: none; } }
/* Media Screen 425px - End
================================================== */
/* Media Screen 375px - Start
================================================== */
@media screen and (max-width: 375px) {
  .video_btn.ripple_effect .btn_icon i {
    width: 70px;
    height: 70px; }

  .video_btn.ripple_effect .btn_icon {
    padding: 14px; }

  .other_posts_nav a:first-child,
  .other_posts_nav a:last-child {
    gap: 20px;
    flex-direction: column;
    padding: 26px 30px 23px; }

  .other_posts_nav a:first-child {
    align-items: flex-start; }
    .other_posts_nav a:first-child span {
      text-align: left; }

  .other_posts_nav a:last-child {
    align-items: flex-end; }
    .other_posts_nav a:last-child i {
      order: -1; }
    .other_posts_nav a:last-child span {
      text-align: right; }

  .pricing_block {
    padding: 40px 20px; }

  .pricing_block .pricing_price_value {
    width: 100%;
    font-size: 40px; }

  .site_footer .copyright_text {
    font-size: 14px; }

  .service_section .shape_image_4, .service_section .shape_image_5, .faq_section .shape_image_2, .blog_section .shape_image_2 {
    max-width: 30px; }

  .about_and_case_section .shape_image_1 img, .process_technology_review_section .shape_image_4 img, .contact_section .shape_image_3 img, .footer_layout_2 .shape_image_1 img {
    width: calc(100% - 60px); }

  .service_section .shape_image_3, .process_technology_review_section .shape_image_4, .faq_section .shape_image_1, .about_and_case_section .shape_image_1, .contact_section .shape_image_3, .footer_layout_2 .shape_image_1 {
    top: -9px; }

  .about_funfact_info {
    width: 100%;
    padding: 30px; }

  .about_funfact_info .about_funfact_counter {
    gap: 20px;
    margin-top: 30px; }

  .about_funfact_info .about_funfact_counter .counter_value {
    font-size: 30px; }

  .process_technology_review_section {
    padding: 100px 0; }

  .site_header_2 .main_menu {
    top: 95px; }

  .site_header_2.sticky .main_menu {
    top: 70px; } }
/* Media Screen 375px - End
================================================== */

/*# sourceMappingURL=style.css.map */
