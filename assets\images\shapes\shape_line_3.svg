<svg width="209" height="670" viewBox="0 0 209 670" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.1" d="M207 2H157.5C145.833 2.33333 121.6 12.8 118 52C114.4 91.2 84.1667 96.3333 69.5 94C51.8333 93 16.4 99.6 16 134C15.6 168.4 15.8333 461.333 16 603.5C15.3333 617.667 19.2 646.9 40 650.5C41.2 650.5 152.833 650.5 208.5 650.5" stroke="#0132B8" stroke-width="3"/>
<g filter="url(#filter0_d_847_438)">
<circle cx="8" cy="8" r="8" transform="matrix(-1 0 0 1 24 178)" fill="#0044EB"/>
</g>
<g filter="url(#filter1_d_847_438)">
<circle cx="8" cy="8" r="8" transform="matrix(-1 0 0 1 87 642)" fill="white"/>
</g>
<defs>
<filter id="filter0_d_847_438" x="0.8" y="174.8" width="30.4" height="30.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3.6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.0313726 0 0 0 0 0.258824 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_847_438"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_847_438" result="shape"/>
</filter>
<filter id="filter1_d_847_438" x="63.8" y="638.8" width="30.4" height="30.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3.6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.0313726 0 0 0 0 0.258824 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_847_438"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_847_438" result="shape"/>
</filter>
</defs>
</svg>
